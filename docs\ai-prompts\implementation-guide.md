# AI Tool Directory - Enhanced Prompt System Implementation Guide

## Overview

This document provides comprehensive documentation for implementing the specialized "AI Dude" prompt structure from `docs/ai-prompts example.md`, adapted specifically for our AI tool directory's content generation needs. The implementation leverages our existing prompt template system while introducing enhanced templates that follow the proven methodology from the example.

## Table of Contents

1. [Prompt Structure Analysis](#prompt-structure-analysis)
2. [Database Schema Integration](#database-schema-integration)
3. [System Prompt Templates](#system-prompt-templates)
4. [User Prompt Templates](#user-prompt-templates)
5. [Variable Definitions](#variable-definitions)
6. [Implementation Strategy](#implementation-strategy)
7. [API Integration](#api-integration)
8. [Admin Interface Updates](#admin-interface-updates)
9. [Testing Strategy](#testing-strategy)
10. [Migration Plan](#migration-plan)

## Prompt Structure Analysis

### Example Methodology Overview

The example file demonstrates a two-part prompt system:

1. **System Message**: Defines the "AI Dude" persona, JSON schema requirements, and tone rules
2. **User Message**: Contains the raw Markdown content to be processed

### Key Components from <PERSON>ample

```javascript
// System Message Structure
{
  persona: "AI Dude - irreverent, no-BS curator",
  jsonSchema: "Exact schema matching our database",
  toneRules: "Snarky, witty, no corporate sugarcoating",
  outputFormat: "Pure JSON, no code fences",
  confidenceScoring: "0.90+ for obvious mappings"
}

// User Message Structure
{
  rawMarkdown: "{{RAW_MARKDOWN}}", // Variable replacement
  toolUrl: "Tool URL for context",
  customInstructions: "Optional additional guidance"
}
```

### JSON Schema Mapping

The example uses this JSON structure, which we need to map to our database schema:

```json
{
  "toolName": "string",
  "toolDescription": "string", 
  "detailedDescription": "string",
  "keyFeatures": ["array"],
  "prosAndCons": {
    "pros": ["array"],
    "cons": ["array"]
  },
  "pricingType": "string",
  "pricingDetails": "string",
  "categories": {
    "primary": "string",
    "secondary": "string", 
    "confidence": "number"
  },
  "sampleQA": [{"question": "string", "answer": "string"}],
  "tags": ["array"],
  "tooltip": "string",
  "haiku": "string",
  "seoKeywords": ["array"],
  "releases": [{"version": "string", "releaseDate": "string", "changes": ["array"]}]
}
```

### Template 2: AI Dude Complete Content Generation (User Prompt)

```json
{
  "name": "AI Dude Complete Content Generation",
  "description": "Primary user prompt for generating comprehensive tool content with all database fields",
  "category": "content",
  "promptType": "user",
  "template": "You are \"AI Dude,\" the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:\n\n{DATABASE_SCHEMA}\n\n**Tone rules:**\n- Always write like a snarky, witty \"AI Dude.\"\n- Keep it punchy: no corporate sugarcoating.\n- Use contractions, slang, and street-smart humor.\n- Never apologize or say \"I'm sorry.\"\n- Write \"description\" as a one-sentence hook.\n- Write \"short_description\" as a punchy card summary (max 150 chars).\n- Make \"detailed_description\" engaging and informative (150-300 words).\n- Create \"meta_title\" and \"meta_description\" that are SEO-optimized but still snarky.\n- Ensure \"category_confidence\" is 0.90+ if obvious; 0.80-0.75 if guessing.\n\n**Field Requirements:**\n- **Required fields**: name, description, short_description, detailed_description, company, category_primary, features, pricing, pros_and_cons, faqs, hashtags, meta_title, meta_description\n- **Optional fields**: Fill when information is available in scraped content\n- **FAQ generation**: Create 3-5 relevant Q&As in AI Dude style\n- **SEO optimization**: meta_title (max 60 chars), meta_description (150-160 chars), meta_keywords (future implementation)\n- **Excluded fields**: logo_url, website, screenshots, claim_info, generated_content (handled by other systems)\n\n**VERY IMPORTANT:**\n- Output exactly one JSON object.\n- Do not wrap it in backticks or code fences.\n- Do not add extra fields or comments.\n- If any section is missing, use appropriate defaults: \"\" for strings, [] for arrays, {} for objects.\n- Always format dates as YYYY-MM-DD.\n- Generate UUIDs for FAQ entries.\n\nTool URL: {toolUrl}\nScraped Content:\n{scrapedContent}\n\nNow read the content above and produce the complete JSON with all required fields.",
  "variables": ["DATABASE_SCHEMA", "toolUrl", "scrapedContent"],
  "validationRules": ["All required fields present", "Field length limits", "SEO optimization"],
  "formatRequirements": "Complete JSON output with all database fields"
}
```

### Template 3: AI Dude Partial Content Generation with Context

```json
{
  "name": "AI Dude Partial Content Generation with Context",
  "description": "User prompt for generating specific sections with existing tool data context",
  "category": "partial",
  "promptType": "user",
  "template": "You are \"AI Dude,\" the irreverent, no-BS curator of AI tools. Generate ONLY the {sectionType} section for this tool in your signature snarky style.\n\n**Existing Tool Data (for context):**\n{existingToolData}\n\n**New Scraped Content:**\n{scrapedContent}\n\n**Tool URL:** {toolUrl}\n\n**Section to Generate:** {sectionType}\n\n**Section Requirements:**\n{sectionRequirements}\n\n**Instructions:**\n- Use the existing tool data for context and consistency\n- Focus ONLY on generating the {sectionType} section\n- Maintain consistency with existing tone and style\n- If updating existing content, improve and enhance it\n- Keep the irreverent, witty \"AI Dude\" voice throughout\n\nOutput only the requested section in JSON format matching the database schema.",
  "variables": ["sectionType", "existingToolData", "scrapedContent", "toolUrl", "sectionRequirements"],
  "validationRules": ["Section-specific validation", "Consistency with existing data"],
  "formatRequirements": "JSON object containing only the requested section with proper schema structure"
}
```

## User Prompt Templates

### Full Content Generation Template

This template follows the exact methodology from the example file:

```javascript
// System Message (stored as system prompt template)
const systemPrompt = `You are "AI Dude," the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

${DATABASE_SCHEMA}

**Tone rules:**
- Always write like a snarky, witty "AI Dude."
- Keep it punchy: no corporate sugarcoating.
- Use contractions, slang, and street-smart humor.
- Never apologize or say "I'm sorry."
- Write "description" as a one-sentence hook.
- Ensure "category_confidence" is 0.90 or above if the category mapping is obvious; if you're guessing, use 0.80 or 0.75.

**VERY IMPORTANT:**
- Output exactly one JSON object.
- Do not wrap it in backticks or code fences.
- Do not add extra fields or comments.
- If any section is missing in the Markdown, leave that JSON field as an empty string (""), empty array ([]), or appropriate default ("unknown").
- Always format dates as YYYY-MM-DD.

Now read the user content and produce the JSON.`;

// User Message (variable replacement)
const userPrompt = `{{RAW_MARKDOWN}}`;
```

### Partial Content Generation Templates

For generating specific sections only:

#### Features Generation with Context
```json
{
  "name": "AI Dude Features Generation with Context",
  "description": "Generate enhanced features section with existing tool context",
  "category": "features",
  "promptType": "user",
  "template": "You are \"AI Dude.\" Generate 3-8 key features for this tool in your snarky style.\n\n**Existing Tool Data:**\n{existingToolData}\n\n**New Scraped Content:**\n{scrapedContent}\n\n**Tool URL:** {toolUrl}\n\n**Instructions:**\n- Generate comprehensive features list (3-8 items)\n- Use existing tool context for consistency\n- Make each feature specific and actionable\n- Keep the irreverent AI Dude tone\n- Focus on what makes this tool unique\n\nOutput JSON: {\"features\": [\"feature1\", \"feature2\", ...]}",
  "variables": ["existingToolData", "scrapedContent", "toolUrl"]
}
```

#### Pricing Generation with Context
```json
{
  "name": "AI Dude Pricing Generation with Context",
  "description": "Generate comprehensive pricing section with existing tool context",
  "category": "pricing",
  "promptType": "user",
  "template": "You are \"AI Dude.\" Analyze and generate pricing information for this tool.\n\n**Existing Tool Data:**\n{existingToolData}\n\n**New Scraped Content:**\n{scrapedContent}\n\n**Tool URL:** {toolUrl}\n\n**Instructions:**\n- Determine pricing type: Free, Paid, Freemium, or Open Source\n- Create detailed pricing plans if available\n- Include pricing description in AI Dude style\n- Be accurate but entertaining\n- Note any free trials or special offers\n\nOutput JSON: {\"pricing\": {\"type\": \"Free|Paid|Freemium|Open Source\", \"plans\": [...], \"details\": \"...\"}}",
  "variables": ["existingToolData", "scrapedContent", "toolUrl"]
}
```

#### Pros/Cons Generation with Context
```json
{
  "name": "AI Dude Pros/Cons Generation with Context",
  "description": "Generate balanced pros and cons with existing tool context",
  "category": "pros_cons",
  "promptType": "user",
  "template": "You are \"AI Dude.\" Generate 3-10 pros and cons for this tool in your irreverent style.\n\n**Existing Tool Data:**\n{existingToolData}\n\n**New Scraped Content:**\n{scrapedContent}\n\n**Tool URL:** {toolUrl}\n\n**Instructions:**\n- Generate 3-10 honest pros (what's genuinely good)\n- Generate 3-10 honest cons (what could be better)\n- Balance positive and negative aspects\n- Use specific, actionable points\n- Maintain snarky but fair AI Dude tone\n- Consider user experience insights\n\nOutput JSON: {\"pros_and_cons\": {\"pros\": [...], \"cons\": [...]}}",
  "variables": ["existingToolData", "scrapedContent", "toolUrl"]
}
```

#### SEO Content Generation with Context
```json
{
  "name": "AI Dude SEO Content Generation with Context",
  "description": "Generate SEO-optimized meta content with existing tool context",
  "category": "seo",
  "promptType": "user",
  "template": "You are \"AI Dude.\" Generate SEO-optimized meta content for this tool.\n\n**Existing Tool Data:**\n{existingToolData}\n\n**New Scraped Content:**\n{scrapedContent}\n\n**Tool URL:** {toolUrl}\n\n**Instructions:**\n- Create compelling meta_title (max 60 characters, SEO-optimized)\n- Write engaging meta_description (150-160 characters, includes CTA)\n- Maintain AI Dude personality while being search-friendly\n- Include relevant keywords naturally\n- Make it click-worthy but accurate\n\nOutput JSON: {\"meta_title\": \"...\", \"meta_description\": \"...\"}",
  "variables": ["existingToolData", "scrapedContent", "toolUrl"]
}
```

#### FAQ Generation with Context
```json
{
  "name": "AI Dude FAQ Generation with Context",
  "description": "Generate comprehensive FAQs with existing tool context",
  "category": "faqs",
  "promptType": "user",
  "template": "You are \"AI Dude.\" Generate 3-5 relevant FAQs for this tool in your signature style.\n\n**Existing Tool Data:**\n{existingToolData}\n\n**New Scraped Content:**\n{scrapedContent}\n\n**Tool URL:** {toolUrl}\n\n**Instructions:**\n- Create 3-5 frequently asked questions\n- Cover different categories: general, pricing, features, support\n- Write answers in AI Dude's irreverent but helpful style\n- Make questions realistic and useful\n- Include proper FAQ metadata structure\n\nOutput JSON: {\"faqs\": [{\"id\": \"uuid\", \"question\": \"...\", \"answer\": \"...\", \"category\": \"general|pricing|features|support\", \"displayOrder\": 0, \"priority\": 5, \"isActive\": true, \"source\": \"ai_generated\", \"sourceMetadata\": {\"aiModel\": \"ai_dude\", \"confidence\": 0.9}}]}",
  "variables": ["existingToolData", "scrapedContent", "toolUrl"]
}
```

## Variable Definitions

### Core Variables

| Variable | Description | Example Value | Usage |
|----------|-------------|---------------|-------|
| `{toolName}` | Name of the AI tool | "SnarkyAI" | All templates |
| `{toolUrl}` | Tool website URL | "https://snarkyai.com" | All templates |
| `{scrapedContent}` | Raw scraped Markdown | "# Tool Name\n\nDescription..." | Content generation |
| `{DATABASE_SCHEMA}` | Complete JSON schema definition | Full schema with all fields | System prompts |
| `{existingToolData}` | **NEW**: Current tool data for context | JSON object with existing fields | **Partial generation** |
| `{sectionType}` | Specific section to generate | "features", "pricing", "pros_cons", "seo", "faqs" | Partial generation |
| `{sectionRequirements}` | Requirements for specific section | "3-8 features, technical details" | Partial generation |

### Enhanced Variables for AI-Generated Fields

| Variable | Description | Example Value | Usage |
|----------|-------------|---------------|-------|
| `{categoryHints}` | Category suggestions | "Content Generation, Marketing" | Category classification |
| `{companyInfo}` | Company/organization details | "OpenAI, Microsoft, etc." | Company field generation |
| `{socialLinks}` | Social media links | {"twitter": "url", "github": "url"} | Social links object |
| `{releaseInfo}` | Version/release information | Version history data | Releases field |
| `{seoKeywords}` | SEO keyword suggestions | ["ai tool", "content generation"] | Meta fields (including future meta_keywords) |
| `{qualityThreshold}` | Minimum quality score | "0.8" | Quality validation |
| `{customInstructions}` | Additional guidance | "Focus on enterprise features" | Custom requirements |

**Variables Removed (Fields Not AI-Generated):**
- `{logoUrl}` - Logo URLs handled by media upload system
- `{screenshotUrls}` - Screenshots handled by media upload system
- `{claimInfo}` - Tool claiming handled by claiming system

### Context-Aware Partial Generation Variables

| Variable | Description | Example Value | Usage |
|----------|-------------|---------------|-------|
| `{existingToolData}` | **Complete existing tool data** | Full tool JSON object | **All partial templates** |
| `{fieldToUpdate}` | Specific field being updated | "features", "pricing", "faqs" | Field-specific updates |
| `{updateReason}` | Reason for the update | "New feature added", "Pricing changed" | Context for updates |
| `{preserveFields}` | Fields to keep unchanged | ["name", "description"] | Selective updates |
| `{enhancementType}` | Type of enhancement | "expand", "improve", "complete" | Enhancement strategy |

## Variable Template Patterns

### Standard Replacement Pattern
```javascript
// Replace variables in template
let processedPrompt = template;
const variableMap = {
  toolName: data.toolName,
  toolUrl: data.toolUrl,
  scrapedContent: data.scrapedContent,
  DATABASE_SCHEMA: JSON.stringify(databaseSchema, null, 2)
};

for (const [variable, value] of Object.entries(variableMap)) {
  const regex = new RegExp(`\\{${variable}\\}`, 'g');
  processedPrompt = processedPrompt.replace(regex, value);
}
```

### Simplified Database Schema Generation (AI-Generated Fields Only)
```javascript
// Generate simplified database schema for AI Dude prompts (AI-generated fields only)
const generateAIDudeDatabaseSchema = () => {
  return {
    // Core identification fields
    name: "string (required, max 255 chars)",
    description: "string (required, brief description)",
    short_description: "string (required, max 150 chars for cards)",
    detailed_description: "string (required, 150-300 words)",

    // Company and categorization
    company: "string (required, company/organization name)",
    category_primary: "string (required, primary category)",
    category_secondary: "string (optional, secondary category)",
    category_confidence: "number (required, 0.0-1.0 confidence score)",

    // Core content fields
    features: ["array of 3-8 feature strings (required)"],
    pricing: {
      type: "Free|Paid|Freemium|Open Source (required)",
      plans: [{"name": "string", "price": "string", "features": ["array"]}],
      details: "string (pricing description)"
    },
    pros_and_cons: {
      pros: ["array of 3-10 pros (required)"],
      cons: ["array of 3-10 cons (required)"]
    },

    // Social and external links
    social_links: {
      twitter: "url or null",
      linkedin: "url or null",
      github: "url or null",
      facebook: "url or null",
      youtube: "url or null"
    },

    // Additional content
    hashtags: ["array of 5-10 hashtags (required)"],
    haiku: {
      lines: ["line1", "line2", "line3"],
      theme: "string"
    },
    releases: [
      {
        version: "string",
        releaseDate: "YYYY-MM-DD",
        changes: ["array of changes"]
      }
    ],

    // FAQ system with complete structure
    faqs: [
      {
        id: "uuid",
        question: "string (required)",
        answer: "string (required)",
        category: "general|pricing|features|support|getting-started",
        displayOrder: "number",
        priority: "number (1-10)",
        isActive: true,
        isFeatured: "boolean",
        source: "ai_generated",
        sourceMetadata: {
          aiModel: "string",
          confidence: "number (0.0-1.0)"
        },
        metaKeywords: "string"
      }
    ],

    // SEO fields
    meta_title: "string (required, max 60 chars, SEO optimized)",
    meta_description: "string (required, 150-160 chars, SEO optimized)",
    meta_keywords: "string (future implementation, SEO keywords comma-separated)"
  };
};

// Fields excluded from AI generation (handled by other systems)
const excludedFields = {
  logo_url: "Handled by media upload system",
  website: "Provided during tool submission",
  screenshots: "Handled by media upload system",
  claim_info: "Handled by claiming system",
  generated_content: "System metadata field"
};

// Generate schema for partial content generation with existing data context
const generatePartialGenerationContext = (existingToolData, sectionType) => {
  return {
    existingData: existingToolData || {},
    targetSection: sectionType,
    requiredFields: getRequiredFieldsForSection(sectionType),
    validationRules: getValidationRulesForSection(sectionType)
  };
};

// Helper function to get required fields for specific sections
const getRequiredFieldsForSection = (sectionType) => {
  const sectionFields = {
    features: ["features"],
    pricing: ["pricing"],
    pros_cons: ["pros_and_cons"],
    seo: ["meta_title", "meta_description"],
    faqs: ["faqs"],
    company: ["company", "website"],
    social: ["social_links"],
    media: ["logo_url", "screenshots"],
    releases: ["releases"],
    content: ["description", "short_description", "detailed_description"]
  };

  return sectionFields[sectionType] || [];
};
```

## Implementation Strategy

### Phase 1: Database Schema Preparation

#### 1.1 Prompt Template Storage Enhancement

The existing `system_configuration` table already supports prompt templates with `config_type = 'prompt_template'`. We need to add specialized templates for the AI Dude methodology:

```sql
-- Insert AI Dude system prompt template
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('prompt_ai_dude_system', '{
  "name": "AI Dude Content Generation System",
  "description": "System prompt defining AI Dude persona and JSON schema requirements",
  "category": "content",
  "promptType": "system",
  "template": "You are \"AI Dude,\" the irreverent, no-BS curator of AI tools...",
  "variables": ["DATABASE_SCHEMA"],
  "validationRules": ["Schema compliance", "Tone consistency", "Format requirements"],
  "formatRequirements": "Pure JSON output matching database schema",
  "usage": 0
}', 'prompt_template', 'AI Dude methodology system prompt');

-- Insert AI Dude user prompt template
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('prompt_ai_dude_user', '{
  "name": "AI Dude Content Generation User",
  "description": "User prompt for AI Dude content generation",
  "category": "content",
  "promptType": "user",
  "template": "Tool URL: {toolUrl}\nScraped Content:\n{scrapedContent}",
  "variables": ["toolUrl", "scrapedContent"],
  "validationRules": [],
  "formatRequirements": "Raw content for AI processing",
  "usage": 0
}', 'prompt_template', 'AI Dude methodology user prompt');
```

#### 1.2 Schema Validation Enhancement

Add validation schema for AI Dude prompts:

```json
{
  "type": "object",
  "properties": {
    "name": {"type": "string", "maxLength": 500},
    "description": {"type": "string", "maxLength": 500},
    "detailed_description": {"type": "string", "minLength": 150, "maxLength": 2000},
    "features": {
      "type": "array",
      "minItems": 3,
      "maxItems": 8,
      "items": {"type": "string"}
    },
    "pricing": {
      "type": "object",
      "properties": {
        "type": {"enum": ["Free", "Paid", "Freemium", "Open Source"]},
        "plans": {"type": "array"}
      }
    },
    "pros_and_cons": {
      "type": "object",
      "properties": {
        "pros": {"type": "array", "minItems": 3, "maxItems": 10},
        "cons": {"type": "array", "minItems": 3, "maxItems": 10}
      }
    },
    "category_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0}
  },
  "required": ["name", "description", "detailed_description", "features", "pros_and_cons"]
}
```

### Phase 2: AI Content Generator Integration

#### 2.1 Enhanced PromptManager Updates

Modify `src/lib/ai/prompt-manager.ts` to support AI Dude methodology:

```typescript
export class PromptManager {
  /**
   * Build AI Dude system prompt with schema injection
   */
  static buildAIDudeSystemPrompt(databaseSchema: any): string {
    const schemaString = JSON.stringify(databaseSchema, null, 2);

    return `You are "AI Dude," the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

${schemaString}

**Tone rules:**
- Always write like a snarky, witty "AI Dude."
- Keep it punchy: no corporate sugarcoating.
- Use contractions, slang, and street-smart humor.
- Never apologize or say "I'm sorry."
- Write "description" as a one-sentence hook.
- Ensure "category_confidence" is 0.90 or above if the category mapping is obvious; if you're guessing, use 0.80 or 0.75.

**VERY IMPORTANT:**
- Output exactly one JSON object.
- Do not wrap it in backticks or code fences.
- Do not add extra fields or comments.
- If any section is missing in the Markdown, leave that JSON field as an empty string (""), empty array ([]), or appropriate default ("unknown").
- Always format dates as YYYY-MM-DD.

Now read the user content and produce the JSON.`;
  }

  /**
   * Build AI Dude user prompt (raw content)
   */
  static buildAIDudeUserPrompt(
    scrapedContent: string,
    toolUrl: string
  ): string {
    return scrapedContent; // Pure content as per example methodology
  }

  /**
   * Get database schema for AI Dude prompts
   */
  static getAIDudeDatabaseSchema(): any {
    return {
      name: "string",
      description: "string (under 500 chars)",
      detailed_description: "string (150-300 words)",
      features: ["array of 3-8 feature strings"],
      pricing: {
        type: "Free|Paid|Freemium|Open Source",
        plans: [{"name": "string", "price": "string", "features": ["array"]}]
      },
      pros_and_cons: {
        pros: ["array of 3-10 pros"],
        cons: ["array of 3-10 cons"]
      },
      category_primary: "string",
      category_secondary: "string",
      category_confidence: "number (0.0-1.0)",
      faqs: [{"question": "string", "answer": "string"}],
      hashtags: ["array of 5-10 hashtags"],
      tooltip: "string (under 100 chars)",
      haiku: {
        lines: ["line1", "line2", "line3"],
        theme: "string"
      },
      meta_keywords: ["array of SEO keywords"],
      releases: [{"version": "string", "releaseDate": "YYYY-MM-DD", "changes": ["array"]}],
      social_links: {
        twitter: "url or null",
        linkedin: "url or null",
        github: "url or null"
      }
    };
  }

  /**
   * Process AI Dude response and map to database schema
   */
  static processAIDudeResponse(aiResponse: any): any {
    // Map AI Dude response to our database schema
    const mapped = {
      name: aiResponse.name || '',
      description: aiResponse.description || '',
      detailed_description: aiResponse.detailed_description || '',
      features: aiResponse.features || [],
      pricing: aiResponse.pricing || { type: 'unknown', plans: [] },
      pros_and_cons: aiResponse.pros_and_cons || { pros: [], cons: [] },
      category_id: aiResponse.category_primary || null,
      subcategory: aiResponse.category_secondary || null,
      faqs: aiResponse.faqs || [],
      hashtags: aiResponse.hashtags || [],
      tooltip: aiResponse.tooltip || '',
      haiku: aiResponse.haiku || { lines: [], theme: '' },
      social_links: aiResponse.social_links || {},
      releases: aiResponse.releases || []
    };

    // Add metadata
    mapped.ai_generation_metadata = {
      methodology: 'ai_dude',
      category_confidence: aiResponse.category_confidence || 0.5,
      generated_at: new Date().toISOString(),
      schema_version: '1.0'
    };

    return mapped;
  }
}
```

#### 2.2 AIContentGenerator Enhancement

Add AI Dude methodology support to `src/lib/ai/content-generator.ts`:

```typescript
export class AIContentGenerator {
  /**
   * Generate content using AI Dude methodology
   */
  async generateContentAIDude(
    scrapedContent: string,
    toolUrl: string,
    options: GenerationOptions = {}
  ): Promise<GenerationResult> {
    try {
      // Build AI Dude prompts
      const systemPrompt = PromptManager.buildAIDudeSystemPrompt(
        PromptManager.getAIDudeDatabaseSchema()
      );
      const userPrompt = PromptManager.buildAIDudeUserPrompt(scrapedContent, toolUrl);

      // Select optimal model
      const modelConfig = ModelSelector.selectOptimalModel({
        contentSize: PromptManager.calculateTokenCount(scrapedContent),
        complexity: options.complexity || 'medium',
        priority: options.priority || 'quality',
        features: ['json_output', 'large_context']
      });

      // Generate content
      const client = modelConfig.provider === 'openai' ? this.openaiClient : this.openrouterClient;

      const response = await client.generateContent(systemPrompt, userPrompt, {
        model: modelConfig.model,
        responseFormat: 'json_object',
        maxTokens: Math.floor(modelConfig.maxTokens * 0.8),
        temperature: 0.7
      });

      // Process and validate response
      const parsedContent = PromptManager.extractJsonFromResponse(response.content);
      const mappedContent = PromptManager.processAIDudeResponse(parsedContent);

      // Validate against schema
      const validation = await this.validateAIDudeContent(mappedContent);

      return {
        success: true,
        content: mappedContent,
        validation,
        modelUsed: modelConfig,
        tokenUsage: response.tokenUsage,
        timestamp: new Date().toISOString(),
        methodology: 'ai_dude'
      };

    } catch (error) {
      log.ai('ai-dude-generation-error', `AI Dude generation failed: ${error.message}`, {
        toolUrl,
        error: error.message
      });

      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
        methodology: 'ai_dude'
      };
    }
  }

  /**
   * Validate AI Dude generated content
   */
  private async validateAIDudeContent(content: any): Promise<ValidationResult> {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Required field validation
    const requiredFields = ['name', 'description', 'detailed_description', 'features', 'pros_and_cons'];
    for (const field of requiredFields) {
      if (!content[field]) {
        issues.push(`Missing required field: ${field}`);
      }
    }

    // Content length validation
    if (content.description && content.description.length > 500) {
      warnings.push('Description exceeds 500 character limit');
    }

    if (content.detailed_description) {
      const wordCount = content.detailed_description.split(' ').length;
      if (wordCount < 50 || wordCount > 300) {
        warnings.push(`Detailed description word count (${wordCount}) outside 50-300 range`);
      }
    }

    // Features validation
    if (content.features && (content.features.length < 3 || content.features.length > 8)) {
      warnings.push(`Features count (${content.features.length}) outside 3-8 range`);
    }

    // Category confidence validation
    if (content.ai_generation_metadata?.category_confidence < 0.75) {
      warnings.push('Low category confidence score');
    }

    return {
      isValid: issues.length === 0,
      issues,
      warnings,
      qualityScore: this.calculateQualityScore(content, issues, warnings),
      validatedAt: new Date().toISOString()
    };
  }

  private calculateQualityScore(content: any, issues: string[], warnings: string[]): number {
    let score = 100;
    score -= issues.length * 20; // Major issues
    score -= warnings.length * 5; // Minor issues

    // Bonus for completeness
    const completenessBonus = this.calculateCompletenessBonus(content);
    score += completenessBonus;

    return Math.max(0, Math.min(100, score));
  }

  private calculateCompletenessBonus(content: any): number {
    let bonus = 0;

    if (content.haiku?.lines?.length === 3) bonus += 5;
    if (content.social_links && Object.keys(content.social_links).length > 0) bonus += 5;
    if (content.releases && content.releases.length > 0) bonus += 5;
    if (content.faqs && content.faqs.length > 0) bonus += 10;

    return bonus;
  }
}
```

### Phase 3: API Integration

#### 3.1 Enhanced quickGenerate Function

Update `src/lib/ai/index.ts` to support AI Dude methodology:

```typescript
export const quickGenerateAIDude = async (
  content: string,
  url: string,
  options?: {
    priority?: any;
    complexity?: any;
    useAIDudeMethodology?: boolean;
  }
): Promise<any> => {
  const { AIContentGenerator } = await import('./content-generator');
  const generator = new AIContentGenerator();

  if (options?.useAIDudeMethodology) {
    return await generator.generateContentAIDude(content, url, options);
  } else {
    return await generator.generateContent(content, url, options);
  }
};
```

#### 3.2 Prompt Template API Enhancement

Update `/api/admin/prompts/test` to support AI Dude templates:

```typescript
// In src/app/api/admin/prompts/test/route.ts
export async function POST(request: NextRequest) {
  // ... existing validation code ...

  const promptTemplate = promptRecord.config_value;

  // Enhanced sample data for AI Dude testing
  const sampleData = testData || {
    toolName: 'SnarkyAI',
    toolUrl: 'https://snarkyai.com',
    scrapedContent: `# SnarkyAI

An unapologetic AI tool that writes blog posts faster than you can type "coffee."

---

## Detailed Description

SnarkyAI is for people who can't stand fluff. It generates long-form blog posts by analyzing your outline and spitting out paragraphs that bite. It pulls in trending memes, makes jokes at your competition's expense, and keeps your SEO on point—so that your readers either love you or hate you, but they can't ignore you.

---

## Key Features

- Outline-to-Publish: Drop in bullet points, get a 2,000-word article in minutes.
- SnarkTone Adjuster: Dial the sarcasm up or down from "mild troll" to "full roast."
- Meme Inserter: Auto-embed relevant memes or GIFs if you ask nicely.

---

## Pros

- Saves time on writer's block—your brain can go watch Netflix.
- Hilariously irreverent; great for edgy brands.
- Built-in SEO keywords and meta tags—no more guesswork.

## Cons

- Not for serious academic stuff; it will roast your thesis.
- Meme Inserter sometimes posts the wrong GIF—oops.
- Pricing is per word, so if you forget to set a limit, expect sticker shock.

---

## Pricing

Paid: $0.02/word; monthly cap of $100.`
  };

  // Check if this is an AI Dude template
  const isAIDudeTemplate = promptTemplate.name?.includes('AI Dude') ||
                          promptTemplate.description?.includes('AI Dude');

  if (isAIDudeTemplate && promptTemplate.promptType === 'user') {
    // Use AI Dude methodology for testing
    aiResponse = await quickGenerateAIDude(
      sampleData.scrapedContent,
      sampleData.toolUrl,
      {
        priority: 'quality',
        complexity: 'medium',
        useAIDudeMethodology: true
      }
    );
  } else {
    // Use existing methodology
    aiResponse = await quickGenerate(
      processedPrompt,
      sampleData.toolUrl,
      {
        priority: 'quality',
        complexity: 'medium'
      }
    );
  }

  // ... rest of existing code ...
}
```

#### 3.3 Content Generation API Enhancement

Update `/api/generate-content` to support AI Dude methodology:

```typescript
// In src/app/api/generate-content/route.ts
export async function POST(request: NextRequest) {
  // ... existing validation code ...

  const { url, options = {}, methodology = 'standard' } = await request.json();

  // Set generation options
  const generationOptions: GenerationOptions = {
    complexity: options.complexity || 'medium',
    priority: options.priority || 'quality',
    contentQuality: options.contentQuality || 70,
    scrapingCost: options.scrapingCost || 0,
    maxRetries: 3,
    useAIDudeMethodology: methodology === 'ai_dude'
  };

  // Initialize AI content generator
  const aiGenerator = new AIContentGenerator();

  // Generate content using selected methodology
  let result;
  if (methodology === 'ai_dude') {
    result = await aiGenerator.generateContentAIDude(
      content,
      url,
      generationOptions
    );
  } else {
    result = await aiGenerator.generateContent(
      content,
      url,
      generationOptions
    );
  }

  // ... rest of existing code ...
}
```

## API Integration

### New Endpoints

#### 1. AI Dude Template Management

```typescript
// GET /api/admin/prompts/ai-dude
// Retrieve AI Dude specific templates
export async function GET(request: NextRequest) {
  const { data: templates } = await supabase
    .from('system_configuration')
    .select('*')
    .eq('config_type', 'prompt_template')
    .ilike('config_value->name', '%AI Dude%')
    .eq('is_active', true);

  return NextResponse.json({ success: true, data: templates });
}

// POST /api/admin/prompts/ai-dude/test
// Test AI Dude templates with enhanced sample data
export async function POST(request: NextRequest) {
  const { templateId, sampleData } = await request.json();

  // Enhanced testing with AI Dude methodology
  const result = await quickGenerateAIDude(
    sampleData.scrapedContent,
    sampleData.toolUrl,
    { useAIDudeMethodology: true }
  );

  return NextResponse.json({ success: true, result });
}
```

#### 2. Schema Validation Endpoint

```typescript
// POST /api/admin/prompts/validate-schema
// Validate AI Dude generated content against database schema
export async function POST(request: NextRequest) {
  const { content } = await request.json();

  const validation = await validateAIDudeContent(content);

  return NextResponse.json({
    success: true,
    validation,
    schemaCompliance: validation.isValid,
    qualityScore: validation.qualityScore
  });
}
```

### Enhanced Response Formats

#### AI Dude Generation Response
```json
{
  "success": true,
  "content": {
    "name": "SnarkyAI",
    "description": "An unapologetic AI tool that writes blog posts faster than you can type \"coffee.\"",
    "detailed_description": "SnarkyAI is for people who can't stand fluff...",
    "features": ["Outline-to-Publish", "SnarkTone Adjuster", "Meme Inserter"],
    "pricing": {
      "type": "Paid",
      "plans": [{"name": "Per-word", "price": "$0.02/word", "features": ["Monthly cap $100"]}]
    },
    "pros_and_cons": {
      "pros": ["Saves time on writer's block", "Hilariously irreverent", "Built-in SEO"],
      "cons": ["Not for academic use", "Meme inserter glitches", "Cost can add up"]
    },
    "category_primary": "Content Generation",
    "category_secondary": "Marketing Automation",
    "category_confidence": 0.95,
    "ai_generation_metadata": {
      "methodology": "ai_dude",
      "category_confidence": 0.95,
      "generated_at": "2025-01-18T10:00:00Z",
      "schema_version": "1.0"
    }
  },
  "validation": {
    "isValid": true,
    "issues": [],
    "warnings": [],
    "qualityScore": 92
  },
  "methodology": "ai_dude",
  "modelUsed": {
    "provider": "openai",
    "model": "gpt-4o-2024-11-20"
  }
}
```

## Admin Interface Updates

### 1. Enhanced Prompt Management Interface

#### Prompt Template Selection Component
```typescript
// src/components/admin/PromptTemplateSelector.tsx
interface PromptTemplateSelectorProps {
  onTemplateSelect: (template: PromptTemplate) => void;
  methodology?: 'standard' | 'ai_dude';
}

export function PromptTemplateSelector({ onTemplateSelect, methodology = 'standard' }: PromptTemplateSelectorProps) {
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [selectedMethodology, setSelectedMethodology] = useState(methodology);

  useEffect(() => {
    fetchTemplates();
  }, [selectedMethodology]);

  const fetchTemplates = async () => {
    const endpoint = selectedMethodology === 'ai_dude'
      ? '/api/admin/prompts/ai-dude'
      : '/api/admin/prompts';

    const response = await fetch(endpoint);
    const data = await response.json();
    setTemplates(data.data || []);
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <button
          onClick={() => setSelectedMethodology('standard')}
          className={`px-4 py-2 rounded ${selectedMethodology === 'standard' ? 'bg-orange-500 text-white' : 'bg-gray-200'}`}
        >
          Standard Templates
        </button>
        <button
          onClick={() => setSelectedMethodology('ai_dude')}
          className={`px-4 py-2 rounded ${selectedMethodology === 'ai_dude' ? 'bg-orange-500 text-white' : 'bg-gray-200'}`}
        >
          AI Dude Templates
        </button>
      </div>

      <div className="grid gap-4">
        {templates.map(template => (
          <div key={template.id} className="border rounded-lg p-4 hover:bg-gray-50">
            <h3 className="font-semibold">{template.name}</h3>
            <p className="text-gray-600 text-sm">{template.description}</p>
            <div className="flex gap-2 mt-2">
              <span className={`px-2 py-1 text-xs rounded ${
                template.promptType === 'system' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
              }`}>
                {template.promptType}
              </span>
              <span className="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">
                {template.category}
              </span>
            </div>
            <button
              onClick={() => onTemplateSelect(template)}
              className="mt-2 px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
            >
              Select Template
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
```

#### AI Dude Template Editor
```typescript
// src/components/admin/AIDudeTemplateEditor.tsx
interface AIDudeTemplateEditorProps {
  template?: PromptTemplate;
  onSave: (template: PromptTemplate) => void;
  onCancel: () => void;
}

export function AIDudeTemplateEditor({ template, onSave, onCancel }: AIDudeTemplateEditorProps) {
  const [formData, setFormData] = useState({
    name: template?.name || '',
    description: template?.description || '',
    category: template?.category || 'content',
    promptType: template?.promptType || 'user',
    template: template?.template || '',
    variables: template?.variables || [],
    validationRules: template?.validationRules || [],
    formatRequirements: template?.formatRequirements || ''
  });

  const [testResult, setTestResult] = useState<any>(null);
  const [testing, setTesting] = useState(false);

  const handleTest = async () => {
    setTesting(true);
    try {
      const response = await fetch('/api/admin/prompts/ai-dude/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: template?.id,
          template: formData
        })
      });

      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      setTesting(false);
    }
  };

  const handleSave = async () => {
    const templateData = {
      ...formData,
      id: template?.id,
      isActive: true,
      lastModified: new Date().toISOString()
    };

    onSave(templateData);
  };

  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-semibold text-yellow-800">AI Dude Methodology</h3>
        <p className="text-yellow-700 text-sm mt-1">
          This template follows the AI Dude methodology with irreverent tone and specific JSON schema requirements.
        </p>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Template Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full border rounded px-3 py-2"
              placeholder="AI Dude Content Generation"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full border rounded px-3 py-2 h-20"
              placeholder="Template description..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Category</label>
              <select
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                className="w-full border rounded px-3 py-2"
              >
                <option value="content">Content</option>
                <option value="features">Features</option>
                <option value="pricing">Pricing</option>
                <option value="pros_cons">Pros/Cons</option>
                <option value="validation">Validation</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Prompt Type</label>
              <select
                value={formData.promptType}
                onChange={(e) => setFormData({...formData, promptType: e.target.value})}
                className="w-full border rounded px-3 py-2"
              >
                <option value="user">User Prompt</option>
                <option value="system">System Prompt</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Variables</label>
            <input
              type="text"
              value={formData.variables.join(', ')}
              onChange={(e) => setFormData({...formData, variables: e.target.value.split(', ').filter(v => v.trim())})}
              className="w-full border rounded px-3 py-2"
              placeholder="toolName, toolUrl, scrapedContent"
            />
            <p className="text-xs text-gray-500 mt-1">Comma-separated variable names</p>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Template Content</label>
            <textarea
              value={formData.template}
              onChange={(e) => setFormData({...formData, template: e.target.value})}
              className="w-full border rounded px-3 py-2 h-64 font-mono text-sm"
              placeholder="You are 'AI Dude,' the irreverent, no-BS curator..."
            />
          </div>

          <div className="flex gap-2">
            <button
              onClick={handleTest}
              disabled={testing}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {testing ? 'Testing...' : 'Test Template'}
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Save Template
            </button>
            <button
              onClick={onCancel}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>

      {testResult && (
        <div className="border rounded-lg p-4 bg-gray-50">
          <h3 className="font-semibold mb-2">Test Results</h3>
          <pre className="text-sm bg-white p-3 rounded border overflow-auto max-h-64">
            {JSON.stringify(testResult, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
```

### 2. Content Generation Interface Enhancement

#### Methodology Selector Component
```typescript
// src/components/admin/ContentGenerationMethodology.tsx
interface ContentGenerationMethodologyProps {
  onMethodologyChange: (methodology: 'standard' | 'ai_dude') => void;
  currentMethodology: 'standard' | 'ai_dude';
}

export function ContentGenerationMethodology({ onMethodologyChange, currentMethodology }: ContentGenerationMethodologyProps) {
  return (
    <div className="bg-white border rounded-lg p-4">
      <h3 className="font-semibold mb-3">Content Generation Methodology</h3>

      <div className="space-y-3">
        <label className="flex items-center space-x-3">
          <input
            type="radio"
            name="methodology"
            value="standard"
            checked={currentMethodology === 'standard'}
            onChange={(e) => onMethodologyChange(e.target.value as 'standard')}
            className="text-orange-500"
          />
          <div>
            <div className="font-medium">Standard Generation</div>
            <div className="text-sm text-gray-600">
              Uses existing prompt templates and generation logic
            </div>
          </div>
        </label>

        <label className="flex items-center space-x-3">
          <input
            type="radio"
            name="methodology"
            value="ai_dude"
            checked={currentMethodology === 'ai_dude'}
            onChange={(e) => onMethodologyChange(e.target.value as 'ai_dude')}
            className="text-orange-500"
          />
          <div>
            <div className="font-medium">AI Dude Methodology</div>
            <div className="text-sm text-gray-600">
              Uses irreverent tone with specialized JSON schema mapping
            </div>
          </div>
        </label>
      </div>

      {currentMethodology === 'ai_dude' && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <h4 className="font-medium text-yellow-800">AI Dude Features:</h4>
          <ul className="text-sm text-yellow-700 mt-1 space-y-1">
            <li>• Irreverent, snarky tone throughout content</li>
            <li>• Enhanced JSON schema with confidence scoring</li>
            <li>• Specialized validation for content quality</li>
            <li>• Optimized for engaging, humorous descriptions</li>
          </ul>
        </div>
      )}
    </div>
  );
}
```

## Testing Strategy

### 1. Unit Testing for AI Dude Components

#### PromptManager Tests
```typescript
// tests/lib/ai/prompt-manager-ai-dude.test.ts
import { PromptManager } from '@/lib/ai/prompt-manager';

describe('PromptManager AI Dude Methodology', () => {
  describe('buildAIDudeSystemPrompt', () => {
    it('should include AI Dude persona and tone rules', () => {
      const schema = { name: 'string', description: 'string' };
      const prompt = PromptManager.buildAIDudeSystemPrompt(schema);

      expect(prompt).toContain('AI Dude');
      expect(prompt).toContain('irreverent, no-BS curator');
      expect(prompt).toContain('snarky, witty');
      expect(prompt).toContain('no corporate sugarcoating');
    });

    it('should inject database schema correctly', () => {
      const schema = { name: 'string', features: ['array'] };
      const prompt = PromptManager.buildAIDudeSystemPrompt(schema);

      expect(prompt).toContain(JSON.stringify(schema, null, 2));
    });

    it('should include format requirements', () => {
      const schema = {};
      const prompt = PromptManager.buildAIDudeSystemPrompt(schema);

      expect(prompt).toContain('Output exactly one JSON object');
      expect(prompt).toContain('Do not wrap in backticks');
      expect(prompt).toContain('YYYY-MM-DD');
    });
  });

  describe('processAIDudeResponse', () => {
    it('should map AI response to database schema', () => {
      const aiResponse = {
        name: 'Test Tool',
        description: 'A test tool',
        detailed_description: 'This is a detailed description',
        features: ['Feature 1', 'Feature 2'],
        pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] },
        category_primary: 'Content Generation',
        category_confidence: 0.95
      };

      const mapped = PromptManager.processAIDudeResponse(aiResponse);

      expect(mapped.name).toBe('Test Tool');
      expect(mapped.category_id).toBe('Content Generation');
      expect(mapped.ai_generation_metadata.methodology).toBe('ai_dude');
      expect(mapped.ai_generation_metadata.category_confidence).toBe(0.95);
    });

    it('should handle missing fields gracefully', () => {
      const aiResponse = { name: 'Test Tool' };
      const mapped = PromptManager.processAIDudeResponse(aiResponse);

      expect(mapped.description).toBe('');
      expect(mapped.features).toEqual([]);
      expect(mapped.pros_and_cons).toEqual({ pros: [], cons: [] });
    });
  });
});
```

#### AIContentGenerator Tests
```typescript
// tests/lib/ai/content-generator-ai-dude.test.ts
import { AIContentGenerator } from '@/lib/ai/content-generator';

describe('AIContentGenerator AI Dude Methodology', () => {
  let generator: AIContentGenerator;

  beforeEach(() => {
    generator = new AIContentGenerator();
  });

  describe('generateContentAIDude', () => {
    it('should generate content using AI Dude methodology', async () => {
      const scrapedContent = `# Test Tool\nA test AI tool for testing.`;
      const toolUrl = 'https://test-tool.com';

      // Mock the AI client response
      const mockResponse = {
        content: JSON.stringify({
          name: 'Test Tool',
          description: 'A snarky test tool',
          detailed_description: 'This tool is for testing purposes and does it with attitude.',
          features: ['Feature 1', 'Feature 2', 'Feature 3'],
          pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] }
        }),
        tokenUsage: { total: 1000 }
      };

      // Mock the client
      jest.spyOn(generator as any, 'openaiClient', 'get').mockReturnValue({
        generateContent: jest.fn().mockResolvedValue(mockResponse)
      });

      const result = await generator.generateContentAIDude(scrapedContent, toolUrl);

      expect(result.success).toBe(true);
      expect(result.methodology).toBe('ai_dude');
      expect(result.content.ai_generation_metadata.methodology).toBe('ai_dude');
    });

    it('should handle generation errors gracefully', async () => {
      const scrapedContent = 'Invalid content';
      const toolUrl = 'https://test-tool.com';

      // Mock error
      jest.spyOn(generator as any, 'openaiClient', 'get').mockReturnValue({
        generateContent: jest.fn().mockRejectedValue(new Error('API Error'))
      });

      const result = await generator.generateContentAIDude(scrapedContent, toolUrl);

      expect(result.success).toBe(false);
      expect(result.error).toContain('API Error');
      expect(result.methodology).toBe('ai_dude');
    });
  });

  describe('validateAIDudeContent', () => {
    it('should validate required fields', async () => {
      const content = {
        name: 'Test Tool',
        description: 'Test description',
        detailed_description: 'A longer test description with enough words to meet requirements.',
        features: ['Feature 1', 'Feature 2', 'Feature 3'],
        pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] }
      };

      const validation = await (generator as any).validateAIDudeContent(content);

      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });

    it('should identify missing required fields', async () => {
      const content = { name: 'Test Tool' };

      const validation = await (generator as any).validateAIDudeContent(content);

      expect(validation.isValid).toBe(false);
      expect(validation.issues.length).toBeGreaterThan(0);
      expect(validation.issues).toContain('Missing required field: description');
    });

    it('should warn about content length issues', async () => {
      const content = {
        name: 'Test Tool',
        description: 'A'.repeat(600), // Too long
        detailed_description: 'Short', // Too short
        features: ['Feature 1', 'Feature 2'],
        pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] }
      };

      const validation = await (generator as any).validateAIDudeContent(content);

      expect(validation.warnings.length).toBeGreaterThan(0);
      expect(validation.warnings.some(w => w.includes('Description exceeds'))).toBe(true);
    });
  });
});
```

### 2. Integration Testing

#### API Endpoint Tests
```typescript
// tests/api/admin/prompts/ai-dude.test.ts
import { POST } from '@/app/api/admin/prompts/ai-dude/test/route';

describe('/api/admin/prompts/ai-dude/test', () => {
  it('should test AI Dude template successfully', async () => {
    const request = new Request('http://localhost/api/admin/prompts/ai-dude/test', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        templateId: 'test-template-id',
        sampleData: {
          toolName: 'Test Tool',
          toolUrl: 'https://test.com',
          scrapedContent: '# Test Tool\nA test tool description.'
        }
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.result).toBeDefined();
  });
});
```

### 3. End-to-End Testing

#### Content Generation Workflow
```typescript
// tests/e2e/ai-dude-workflow.test.ts
describe('AI Dude Content Generation Workflow', () => {
  it('should complete full content generation using AI Dude methodology', async () => {
    // 1. Create AI Dude template
    const template = await createAIDudeTemplate();
    expect(template.id).toBeDefined();

    // 2. Test template
    const testResult = await testAIDudeTemplate(template.id);
    expect(testResult.success).toBe(true);

    // 3. Generate content for real tool
    const generationResult = await generateContentWithAIDude({
      url: 'https://example-tool.com',
      methodology: 'ai_dude'
    });
    expect(generationResult.success).toBe(true);
    expect(generationResult.methodology).toBe('ai_dude');

    // 4. Validate generated content
    const validation = await validateGeneratedContent(generationResult.content);
    expect(validation.isValid).toBe(true);
    expect(validation.qualityScore).toBeGreaterThan(70);
  });
});
```

### 4. Performance Testing

#### Load Testing for AI Dude Generation
```typescript
// tests/performance/ai-dude-load.test.ts
describe('AI Dude Performance Tests', () => {
  it('should handle concurrent AI Dude generations', async () => {
    const concurrentRequests = 10;
    const promises = Array(concurrentRequests).fill(null).map(() =>
      generateContentWithAIDude({
        url: 'https://test-tool.com',
        methodology: 'ai_dude'
      })
    );

    const results = await Promise.all(promises);

    expect(results.every(r => r.success)).toBe(true);
    expect(results.every(r => r.methodology === 'ai_dude')).toBe(true);
  });

  it('should complete generation within acceptable time limits', async () => {
    const startTime = Date.now();

    const result = await generateContentWithAIDude({
      url: 'https://test-tool.com',
      methodology: 'ai_dude'
    });

    const duration = Date.now() - startTime;

    expect(result.success).toBe(true);
    expect(duration).toBeLessThan(30000); // 30 seconds max
  });
});
```

## Simplified Development Implementation Plan

### Immediate Implementation (Development Environment)

#### Step 1: Database Updates (30 minutes)
1. **Add complete AI Dude prompt templates directly**
   ```sql
   -- Insert complete AI Dude system prompt with all fields
   INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
   ('prompt_ai_dude_complete_system', '{
     "name": "AI Dude Complete Content Generation System",
     "description": "System prompt for complete tool content generation with all database fields",
     "category": "content",
     "promptType": "system",
     "template": "Complete system prompt with full schema...",
     "variables": ["DATABASE_SCHEMA"],
     "validationRules": ["All required fields", "Field length limits", "SEO optimization"],
     "usage": 0
   }', 'prompt_template', 'AI Dude complete content generation system prompt');

   -- Insert AI Dude partial generation prompt
   INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
   ('prompt_ai_dude_partial_context', '{
     "name": "AI Dude Partial Generation with Context",
     "description": "User prompt for partial content generation with existing tool data context",
     "category": "partial",
     "promptType": "user",
     "template": "Partial generation template with context...",
     "variables": ["existingToolData", "scrapedContent", "sectionType"],
     "validationRules": ["Section-specific validation"],
     "usage": 0
   }', 'prompt_template', 'AI Dude partial generation with context');
   ```

2. **Test prompt retrieval immediately**
3. **No backup needed - development environment**

#### Step 2: Code Implementation (2-3 hours)
1. **Update PromptManager with complete field mapping**
   - Add `getCompleteDatabaseSchema()` method
   - Add `processCompleteAIDudeResponse()` method
   - Add `buildPartialPromptWithContext()` method

2. **Update AIContentGenerator**
   - Add `generateCompleteContentAIDude()` method
   - Add `generatePartialContentAIDude()` method
   - Add validation for all database fields

3. **Update API endpoints immediately**
   - Modify `/api/generate-content` for methodology selection
   - Add complete field support
   - Add partial generation with context

#### Step 3: Admin Interface Updates (1-2 hours)
1. **Add methodology selector to existing interfaces**
2. **Update content generation forms**
3. **Add field-specific generation options**

#### Step 4: Testing and Validation (1 hour)
1. **Test complete content generation**
2. **Test partial content generation with context**
3. **Validate all database fields are populated**
4. **Test admin interface functionality**

### Direct Implementation Approach

#### No Complex Migration Strategy
- **Direct database updates** - Insert templates immediately
- **Immediate code deployment** - No gradual rollout needed
- **Simple testing** - Basic functionality validation
- **No rollback planning** - Development environment allows quick fixes

#### Simplified Validation
- **Basic field validation** - Ensure all required fields present
- **Simple quality checks** - Basic content length and format validation
- **Direct error handling** - Log errors, fix immediately

#### Development-Focused Features
- **Enhanced debugging** - Detailed logging for development
- **Quick iteration** - Fast feedback loop for prompt improvements
- **Flexible testing** - Easy template modifications and testing

### Implementation Checklist

#### Database (✓ Complete in 30 minutes)
- [ ] Insert complete AI Dude system prompt template
- [ ] Insert partial generation prompt template
- [ ] Test prompt retrieval functionality
- [ ] Verify template structure and variables

#### Code Updates (✓ Complete in 2-3 hours)
- [ ] Update PromptManager with complete schema
- [ ] Add partial generation with context support
- [ ] Update AIContentGenerator methods
- [ ] Modify API endpoints for methodology selection
- [ ] Add complete field validation

#### Admin Interface (✓ Complete in 1-2 hours)
- [ ] Add methodology selector component
- [ ] Update content generation forms
- [ ] Add partial generation options
- [ ] Test admin interface functionality

#### Testing (✓ Complete in 1 hour)
- [ ] Test complete content generation
- [ ] Test partial content with existing data context
- [ ] Validate all database fields populated correctly
- [ ] Test error handling and validation

### Total Implementation Time: 4-6 hours

This simplified approach eliminates complex migration strategies, rollback plans, and production-level concerns, focusing on direct implementation suitable for a development environment.

### Success Criteria

#### Technical Metrics
- [ ] All unit tests pass (>95% coverage)
- [ ] Integration tests pass (100%)
- [ ] API response times <2 seconds
- [ ] Error rate <1%
- [ ] Quality scores >80 average

#### Functional Metrics
- [ ] AI Dude templates generate valid JSON
- [ ] Content quality meets editorial standards
- [ ] Admin interface is intuitive and functional
- [ ] Migration completes without data loss
- [ ] Backward compatibility maintained

#### User Acceptance
- [ ] Editorial team approves content quality
- [ ] Admin users can create and manage AI Dude templates
- [ ] Content generation workflow is smooth
- [ ] Documentation is complete and clear

## Conclusion

This implementation guide provides a comprehensive roadmap for integrating the AI Dude prompt methodology into our existing AI tool directory system. The approach maintains backward compatibility while introducing enhanced content generation capabilities with improved tone consistency and quality validation.

The phased implementation strategy ensures minimal risk while providing clear milestones and success criteria. The extensive testing strategy validates both technical functionality and content quality, ensuring the new system meets our editorial standards and user expectations.
```

## Database Schema Integration

### AI-Generated Fields Mapping (Simplified Scope)

Based on our tools table structure, here's the mapping for fields that will be AI-generated:

| Database Field | AI Dude JSON Field | Type | Description | AI Generation Priority |
|----------------|-------------------|------|-------------|----------------------|
| `name` | `name` | varchar(255) | Tool display name | **Required** |
| `description` | `description` | text | Brief tool description | **Required** |
| `short_description` | `short_description` | varchar(150) | Truncated description for cards | **Required** |
| `detailed_description` | `detailed_description` | text | Comprehensive tool description | **Required** |
| `company` | `company` | varchar(255) | Company/organization name | **Required** |
| `category_id` | `category_primary` | varchar(255) | Primary category | **Required** |
| `subcategory` | `category_secondary` | varchar(255) | Secondary category | Optional |
| `features` | `features` | jsonb | Array of tool features | **Required** |
| `pricing` | `pricing` | jsonb | Pricing information object | **Required** |
| `social_links` | `social_links` | jsonb | Social media links object | Optional |
| `pros_and_cons` | `pros_and_cons` | jsonb | Pros and cons arrays | **Required** |
| `haiku` | `haiku` | jsonb | AI-generated haiku object | Optional |
| `hashtags` | `hashtags` | jsonb | Array of hashtags/keywords | **Required** |
| `releases` | `releases` | jsonb | Version release information | Optional |
| `faqs` | `faqs` | jsonb | FAQ array structure | **Required** |
| `meta_title` | `meta_title` | varchar(255) | SEO meta title | **Required** |
| `meta_description` | `meta_description` | text | SEO meta description | **Required** |
| `meta_keywords` | `meta_keywords` | text | SEO keywords | **Future Implementation** |

**Fields NOT Generated by AI (Handled by Other Systems):**
- `logo_url` (text) - Tool logo/icon URL - *Handled by media upload system*
- `website` (text) - Official tool website URL - *Provided during tool submission*
- `screenshots` (jsonb) - Array of screenshot URLs - *Handled by media upload system*
- `claim_info` (jsonb) - Tool claiming information - *Handled by claiming system*
- `generated_content` (jsonb) - AI-generated content storage - *System metadata field*

**Field Priority Levels:**
- **Required**: Must be generated by AI for complete tool profile
- **Optional**: Generated when information is available in scraped content
- **Future Implementation**: Field will be added to database schema later

**Note on meta_keywords**: This field will be added to the Supabase database schema in a future update. Include in AI generation templates but mark as future implementation.

### Simplified AI Dude JSON Schema (AI-Generated Fields Only)

```json
{
  "name": "string (required, max 255 chars)",
  "description": "string (required, brief description)",
  "short_description": "string (required, max 150 chars for cards)",
  "detailed_description": "string (required, 150-300 words)",
  "company": "string (required, company/organization name)",
  "category_primary": "string (required, primary category)",
  "category_secondary": "string (optional, secondary category)",
  "category_confidence": "number (required, 0.0-1.0 confidence score)",
  "features": ["array of 3-8 feature strings (required)"],
  "pricing": {
    "type": "Free|Paid|Freemium|Open Source (required)",
    "plans": [{"name": "string", "price": "string", "features": ["array"]}],
    "details": "string (pricing description)"
  },
  "social_links": {
    "twitter": "url or null",
    "linkedin": "url or null",
    "github": "url or null",
    "facebook": "url or null",
    "youtube": "url or null"
  },
  "pros_and_cons": {
    "pros": ["array of 3-10 pros (required)"],
    "cons": ["array of 3-10 cons (required)"]
  },
  "haiku": {
    "lines": ["line1", "line2", "line3"],
    "theme": "string"
  },
  "hashtags": ["array of 5-10 hashtags (required)"],
  "releases": [
    {
      "version": "string",
      "releaseDate": "YYYY-MM-DD",
      "changes": ["array of changes"]
    }
  ],
  "faqs": [
    {
      "id": "uuid",
      "question": "string (required)",
      "answer": "string (required)",
      "category": "general|pricing|features|support|getting-started",
      "displayOrder": "number",
      "priority": "number (1-10)",
      "isActive": true,
      "isFeatured": "boolean",
      "source": "ai_generated",
      "sourceMetadata": {
        "aiModel": "string",
        "confidence": "number (0.0-1.0)"
      },
      "metaKeywords": "string"
    }
  ],
  "meta_title": "string (required, max 60 chars, SEO optimized)",
  "meta_description": "string (required, 150-160 chars, SEO optimized)",
  "meta_keywords": "string (future implementation, SEO keywords comma-separated)"
}
```

**Fields Excluded from AI Generation:**
- `logo_url` - Handled by media upload system
- `website` - Provided during tool submission
- `screenshots` - Handled by media upload system
- `claim_info` - Handled by claiming system
- `generated_content` - System metadata field

**Future Implementation Note:**
The `meta_keywords` field will be added to the Supabase database schema in a future update. Include in AI generation templates but mark as future implementation.

## System Prompt Templates

### Template 1: AI Dude Content Validation (System Prompt)

```json
{
  "name": "AI Dude Content Validation System",
  "description": "System prompt for validating AI-generated content using AI Dude methodology",
  "category": "validation",
  "promptType": "system",
  "template": "You are \"AI Dude,\" the irreverent, no-BS curator of AI tools. Your job is to validate AI-generated content and ensure it matches our exact database schema.\n\nVALIDATION SCHEMA:\n{DATABASE_SCHEMA}\n\nTONE VALIDATION RULES:\n- Always write like a snarky, witty \"AI Dude\"\n- Keep it punchy: no corporate sugarcoating\n- Use contractions, slang, and street-smart humor\n- Never apologize or say \"I'm sorry\"\n- Write descriptions as one-sentence hooks\n- Ensure confidence scores are 0.90+ for obvious mappings\n\nFORMAT REQUIREMENTS:\n- Output exactly one JSON object\n- Do not wrap in backticks or code fences\n- Do not add extra fields or comments\n- Always format dates as YYYY-MM-DD\n- If sections missing, use empty string (\"\"), empty array ([]), or \"unknown\"\n\nValidate the provided content and return validation results in JSON format.",
  "variables": ["DATABASE_SCHEMA"],
  "validationRules": [
    "Schema compliance check",
    "Tone consistency validation", 
    "Required field presence",
    "Data type validation",
    "Content length limits"
  ],
  "formatRequirements": "JSON validation response with pass/fail status and specific issues"
}
```
