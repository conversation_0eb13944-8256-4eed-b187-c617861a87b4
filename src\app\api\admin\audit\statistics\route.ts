/**
 * Audit Trail Statistics API Endpoint
 * Provides audit analytics and dashboard data
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { adminAuditLogger } from '@/lib/audit/admin-audit-logger';
import { log } from '@/lib/logging/logger';

/**
 * GET /api/admin/audit/statistics
 * Get audit statistics and analytics data
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '24h';
    const includeBreakdowns = searchParams.get('includeBreakdowns') !== 'false';

    // Validate time range
    const validTimeRanges = ['1h', '24h', '7d', '30d'];
    if (!validTimeRanges.includes(timeRange)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid time range. Must be one of: ${validTimeRanges.join(', ')}` 
        },
        { status: 400 }
      );
    }

    const statistics = await adminAuditLogger.getAuditStatistics(timeRange);

    // Add additional computed metrics
    const enhancedStatistics: any = {
      ...statistics,
      timeRange,
      successRate: statistics.totalActions > 0
        ? ((statistics.successfulActions / statistics.totalActions) * 100).toFixed(2)
        : '0.00',
      failureRate: statistics.totalActions > 0
        ? ((statistics.failedActions / statistics.totalActions) * 100).toFixed(2)
        : '0.00',
      suspiciousActivityRate: statistics.totalActions > 0
        ? ((statistics.suspiciousActivities / statistics.totalActions) * 100).toFixed(2)
        : '0.00',
      generatedAt: new Date().toISOString()
    };

    // Remove breakdowns if not requested to reduce response size
    if (!includeBreakdowns) {
      delete enhancedStatistics.actionBreakdown;
      delete enhancedStatistics.resourceBreakdown;
      delete enhancedStatistics.topUsers;
      delete enhancedStatistics.recentActivity;
    }

    log.admin('audit_statistics_retrieved', `Audit statistics retrieved for ${timeRange}`, {
      timeRange,
      totalActions: statistics.totalActions,
      includeBreakdowns
    });

    return NextResponse.json({
      success: true,
      data: enhancedStatistics
    });

  } catch (error) {
    log.error('Error fetching audit statistics', error as Error, {
      component: 'audit-statistics-api',
      operation: 'get_audit_statistics'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch audit statistics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/audit/statistics
 * Generate custom audit statistics report
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const reportRequest = await request.json();

    // Validate required fields
    if (!reportRequest.timeRange) {
      return NextResponse.json(
        { success: false, error: 'timeRange is required' },
        { status: 400 }
      );
    }

    // Get base statistics
    const baseStats = await adminAuditLogger.getAuditStatistics(reportRequest.timeRange);

    // Generate custom report based on request parameters
    const customReport: any = {
      ...baseStats,
      timeRange: reportRequest.timeRange,
      reportType: reportRequest.reportType || 'standard',
      generatedAt: new Date().toISOString(),
      requestedBy: reportRequest.requestedBy || 'system'
    };

    // Add specific metrics based on report type
    switch (reportRequest.reportType) {
      case 'security':
        customReport.securityMetrics = {
          suspiciousActivities: baseStats.suspiciousActivities,
          failedActions: baseStats.failedActions,
          securityActionCount: Object.entries(baseStats.actionBreakdown || {})
            .filter(([action]) => action.includes('delete') || action.includes('config'))
            .reduce((sum, [, count]) => sum + count, 0),
          errorRate: baseStats.errorRate
        };
        break;

      case 'performance':
        customReport.performanceMetrics = {
          totalActions: baseStats.totalActions,
          successRate: ((baseStats.successfulActions / baseStats.totalActions) * 100).toFixed(2),
          errorRate: baseStats.errorRate,
          topActiveUsers: baseStats.topUsers?.slice(0, 5) || []
        };
        break;

      case 'compliance':
        customReport.complianceMetrics = {
          totalAuditedActions: baseStats.totalActions,
          dataRetentionCompliance: true, // This would be calculated based on actual retention policies
          auditCoverage: '100%', // This would be calculated based on auditable vs audited operations
          complianceScore: 95 // This would be calculated based on various compliance factors
        };
        break;

      default:
        // Standard report includes all data
        break;
    }

    // Apply custom filters if provided
    if (reportRequest.filters) {
      // This would apply additional filtering logic based on the filters
      // For now, we'll just include the filter information in the response
      customReport.appliedFilters = reportRequest.filters;
    }

    log.admin('custom_audit_report_generated', `Custom audit report generated`, {
      reportType: reportRequest.reportType,
      timeRange: reportRequest.timeRange,
      totalActions: baseStats.totalActions,
      requestedBy: reportRequest.requestedBy
    });

    return NextResponse.json({
      success: true,
      data: customReport
    });

  } catch (error) {
    log.error('Error generating custom audit report', error as Error, {
      component: 'audit-statistics-api',
      operation: 'generate_custom_report'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to generate custom audit report' },
      { status: 500 }
    );
  }
}
