/**
 * Unit Tests for Cost Optimization Strategies
 * Tests pattern matching, credit calculation, optimization decisions, and savings validation
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { CostOptimizer } from '@/lib/scraping/cost-optimizer';
import { ContentAnalyzer } from '@/lib/scraping/content-analyzer';

// Mock the scrape-do client
jest.mock('@/lib/scraping/scrape-do-client', () => ({
  scrapeDoClient: {
    scrapePage: jest.fn(),
    getUsageStatistics: jest.fn()
  }
}));

// Import the mocked module
import { scrapeDoClient } from '@/lib/scraping/scrape-do-client';
const mockScrapeDoClient = scrapeDoClient as jest.Mocked<typeof scrapeDoClient>;

// Mock the content analyzer
jest.mock('@/lib/scraping/content-analyzer', () => ({
  ContentAnalyzer: jest.fn().mockImplementation(() => ({
    analyzeContentQuality: jest.fn()
  }))
}));

const mockContentAnalyzer = {
  analyzeContentQuality: jest.fn()
};

describe('Cost Optimization Strategies', () => {
  let costOptimizer: CostOptimizer;

  beforeEach(() => {
    costOptimizer = new CostOptimizer();
    jest.clearAllMocks();
  });

  describe('Pattern-Based Optimization', () => {
    it('should identify never-enhance patterns correctly', () => {
      const neverEnhanceUrls = [
        'https://wikipedia.org/wiki/test',
        'https://github.com/user/repo',
        'https://stackoverflow.com/questions/123',
        'https://reddit.com/r/programming',
        'https://medium.com/@author/article',
        'https://dev.to/author/post',
        'https://docs.example.com/guide',
        'https://blog.example.com/post',
        'https://news.example.com/article',
        'https://university.edu/page'
      ];

      const categorized = costOptimizer.categorizeUrlsByPattern(neverEnhanceUrls);

      expect(categorized.neverEnhance).toHaveLength(neverEnhanceUrls.length);
      expect(categorized.alwaysEnhance).toHaveLength(0);
      expect(categorized.intelligent).toHaveLength(0);
    });

    it('should identify always-enhance patterns correctly', () => {
      const alwaysEnhanceUrls = [
        'https://claude.ai/chat',
        'https://chat.openai.com/chat',
        'https://bard.google.com',
        'https://notion.so/workspace',
        'https://figma.com/file',
        'https://canva.com/design',
        'https://miro.com/app',
        'https://discord.com/channels',
        'https://app.example.com/dashboard',
        'https://dashboard.example.com/admin',
        'https://admin.example.com/panel'
      ];

      const categorized = costOptimizer.categorizeUrlsByPattern(alwaysEnhanceUrls);

      expect(categorized.alwaysEnhance).toHaveLength(alwaysEnhanceUrls.length);
      expect(categorized.neverEnhance).toHaveLength(0);
      expect(categorized.intelligent).toHaveLength(0);
    });

    it('should categorize mixed URLs correctly', () => {
      const mixedUrls = [
        'https://github.com/user/repo', // never-enhance
        'https://app.example.com/dashboard', // always-enhance
        'https://example.com/pricing', // intelligent
        'https://wikipedia.org/wiki/test', // never-enhance
        'https://claude.ai/chat', // always-enhance
        'https://random-site.com/page' // intelligent
      ];

      const categorized = costOptimizer.categorizeUrlsByPattern(mixedUrls);

      expect(categorized.neverEnhance).toHaveLength(2);
      expect(categorized.alwaysEnhance).toHaveLength(2);
      expect(categorized.intelligent).toHaveLength(2);
    });
  });

  describe('Cost-Optimized Scraping', () => {
    it('should use basic scraping for never-enhance patterns', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Basic content from GitHub',
        metadata: { creditsUsed: 1, requestType: 'Datacenter' }
      });

      const result = await costOptimizer.scrapeWithMaxCostOptimization('https://github.com/user/repo');

      expect(result.success).toBe(true);
      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith(
        'https://github.com/user/repo',
        expect.objectContaining({
          useResidentialProxy: false,
          enableJSRendering: false,
          outputFormat: 'markdown',
          timeout: 15000
        })
      );
    });

    it('should use enhanced scraping for always-enhance patterns', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Enhanced content from Claude',
        metadata: { creditsUsed: 5, requestType: 'Datacenter + Browser' }
      });

      const result = await costOptimizer.scrapeWithMaxCostOptimization('https://claude.ai/chat');

      expect(result.success).toBe(true);
      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith(
        'https://claude.ai/chat',
        expect.objectContaining({
          useResidentialProxy: false,
          enableJSRendering: true,
          waitCondition: 'networkidle2',
          customWaitTime: 3000,
          blockResources: true,
          timeout: 45000
        })
      );
    });

    it('should use intelligent optimization for unknown patterns', async () => {
      // Mock basic scraping first
      mockScrapeDoClient.scrapePage
        .mockResolvedValueOnce({
          success: true,
          content: 'Basic content that needs enhancement',
          metadata: { creditsUsed: 1, requestType: 'Datacenter' }
        })
        .mockResolvedValueOnce({
          success: true,
          content: 'Enhanced content after analysis',
          metadata: { creditsUsed: 5, requestType: 'Datacenter + Browser' }
        });

      // Mock content analysis indicating enhancement needed
      mockContentAnalyzer.analyzeContentQuality
        .mockReturnValueOnce({
          needsEnhancedScraping: true,
          confidence: 0.3,
          scenario: 'Insufficient content'
        })
        .mockReturnValueOnce({
          needsEnhancedScraping: false,
          confidence: 0.9,
          scenario: 'High quality content'
        });

      const result = await costOptimizer.scrapeWithMaxCostOptimization('https://unknown-site.com');

      expect(result.success).toBe(true);
      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledTimes(2);
    });
  });

  describe('Content Quality Analysis', () => {
    it('should determine when content is good enough', () => {
      const goodContent = `
        # Product Title
        
        This is a comprehensive description of the product with detailed information
        about features, pricing, and benefits. The content is well-structured and
        provides valuable information for users.
        
        ## Features
        - Feature 1: Detailed description
        - Feature 2: Another detailed description
        
        ## Pricing
        Starting at $29/month with various plans available.
      `;

      const isGoodEnough = costOptimizer.isGoodEnoughContent(goodContent);

      expect(isGoodEnough).toBe(true);
    });

    it('should identify insufficient content', () => {
      const poorContent = `
        Loading...
        Please wait while we load the content.
      `;

      const isGoodEnough = costOptimizer.isGoodEnoughContent(poorContent);

      expect(isGoodEnough).toBe(false);
    });

    it('should perform cost-benefit analysis', () => {
      const insufficientContent = 'Loading... Please wait.';
      const url = 'https://important-site.com';

      const analysis = costOptimizer.calculateCostBenefit(insufficientContent, url);

      expect(analysis.worthEnhancing).toBe(true);
      expect(analysis.reasoning).toContain('insufficient');
      expect(analysis.enhancementProbability).toBeGreaterThan(0.5);
    });

    it('should avoid enhancement for low-value content', () => {
      const lowValueContent = 'Error 404 - Page not found';
      const url = 'https://broken-site.com';

      const analysis = costOptimizer.calculateCostBenefit(lowValueContent, url);

      expect(analysis.worthEnhancing).toBe(false);
      expect(analysis.reasoning).toContain('error');
    });
  });

  describe('Credit Management', () => {
    it('should track credits used across requests', async () => {
      const testUrls = [
        'https://github.com/user/repo1', // 1 credit
        'https://github.com/user/repo2', // 1 credit
        'https://claude.ai/chat1', // 5 credits
        'https://claude.ai/chat2' // 5 credits
      ];

      mockScrapeDoClient.scrapePage
        .mockResolvedValueOnce({
          success: true,
          content: 'Content 1',
          metadata: { creditsUsed: 1 }
        })
        .mockResolvedValueOnce({
          success: true,
          content: 'Content 2',
          metadata: { creditsUsed: 1 }
        })
        .mockResolvedValueOnce({
          success: true,
          content: 'Content 3',
          metadata: { creditsUsed: 5 }
        })
        .mockResolvedValueOnce({
          success: true,
          content: 'Content 4',
          metadata: { creditsUsed: 5 }
        });

      let totalCredits = 0;
      for (const url of testUrls) {
        const result = await costOptimizer.scrapeWithMaxCostOptimization(url);
        totalCredits += result.metadata?.creditsUsed || 0;
      }

      expect(totalCredits).toBe(12); // 1+1+5+5
    });

    it('should estimate savings from optimization', () => {
      const urls = [
        'https://github.com/user/repo', // Saved 4 credits (5-1)
        'https://wikipedia.org/wiki/test', // Saved 4 credits (5-1)
        'https://claude.ai/chat' // No savings (would use 5 anyway)
      ];

      const categorized = costOptimizer.categorizeUrlsByPattern(urls);
      
      // Calculate potential savings
      const neverEnhanceSavings = categorized.neverEnhance.length * 4; // 4 credits saved per URL
      const expectedSavings = neverEnhanceSavings;

      expect(expectedSavings).toBe(8); // 2 URLs × 4 credits saved
    });
  });

  describe('Optimization Strategies', () => {
    it('should implement ultra-basic scraping correctly', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Ultra-basic content',
        metadata: { creditsUsed: 1, requestType: 'Datacenter' }
      });

      await costOptimizer.scrapeWithMaxCostOptimization('https://github.com/user/repo');

      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith(
        'https://github.com/user/repo',
        expect.objectContaining({
          useResidentialProxy: false,
          enableJSRendering: false,
          outputFormat: 'markdown',
          timeout: 15000,
          deviceType: 'desktop'
        })
      );
    });

    it('should implement enhanced scraping with fallbacks', async () => {
      // Mock enhanced scraping with datacenter proxy
      mockScrapeDoClient.scrapePage
        .mockResolvedValueOnce({
          success: true,
          content: 'Enhanced content',
          metadata: { creditsUsed: 5, requestType: 'Datacenter + Browser' }
        });

      mockContentAnalyzer.analyzeContentQuality.mockReturnValue({
        needsEnhancedScraping: false,
        confidence: 0.9,
        scenario: 'High quality content'
      });

      await costOptimizer.scrapeWithMaxCostOptimization('https://claude.ai/chat');

      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith(
        'https://claude.ai/chat',
        expect.objectContaining({
          useResidentialProxy: false,
          enableJSRendering: true,
          waitCondition: 'networkidle2',
          blockResources: true
        })
      );
    });

    it('should fallback to residential proxy when needed', async () => {
      // Mock enhanced scraping failure, then residential proxy success
      mockScrapeDoClient.scrapePage
        .mockResolvedValueOnce({
          success: true,
          content: 'Still insufficient content',
          metadata: { creditsUsed: 5 }
        })
        .mockResolvedValueOnce({
          success: true,
          content: 'High quality content with residential proxy',
          metadata: { creditsUsed: 50, requestType: 'Residential + Browser' }
        });

      mockContentAnalyzer.analyzeContentQuality
        .mockReturnValueOnce({
          needsEnhancedScraping: true,
          confidence: 0.2,
          scenario: 'Still insufficient after enhancement'
        })
        .mockReturnValueOnce({
          needsEnhancedScraping: false,
          confidence: 0.95,
          scenario: 'Excellent content quality'
        });

      const result = await costOptimizer.scrapeWithMaxCostOptimization('https://difficult-site.com');

      expect(result.success).toBe(true);
      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledTimes(2);
      
      // Second call should use residential proxy
      const secondCall = mockScrapeDoClient.scrapePage.mock.calls[1];
      expect(secondCall[1]).toEqual(expect.objectContaining({
        useResidentialProxy: true,
        enableJSRendering: true,
        geoTargeting: 'us'
      }));
    });
  });

  describe('Performance Metrics', () => {
    it('should measure optimization effectiveness', async () => {
      const testScenarios = [
        {
          url: 'https://github.com/user/repo',
          expectedCredits: 1,
          expectedSavings: 4
        },
        {
          url: 'https://claude.ai/chat',
          expectedCredits: 5,
          expectedSavings: 0
        }
      ];

      for (const scenario of testScenarios) {
        mockScrapeDoClient.scrapePage.mockResolvedValueOnce({
          success: true,
          content: 'Test content',
          metadata: { creditsUsed: scenario.expectedCredits }
        });

        const result = await costOptimizer.scrapeWithMaxCostOptimization(scenario.url);

        expect(result.metadata?.creditsUsed).toBe(scenario.expectedCredits);
      }
    });

    it('should calculate savings percentage', () => {
      const totalUrls = 10;
      const neverEnhanceUrls = 6; // 60% of URLs
      const alwaysEnhanceUrls = 2; // 20% of URLs
      const intelligentUrls = 2; // 20% of URLs

      // Calculate savings
      const neverEnhanceSavings = neverEnhanceUrls * 4; // 4 credits saved per URL
      const intelligentSavings = intelligentUrls * 2; // Assume 50% enhancement rate
      const totalSavings = neverEnhanceSavings + intelligentSavings;
      const totalCostWithoutOptimization = totalUrls * 5; // All enhanced
      const savingsPercentage = (totalSavings / totalCostWithoutOptimization) * 100;

      expect(savingsPercentage).toBeGreaterThan(50); // Should achieve >50% savings
      expect(savingsPercentage).toBeLessThan(70); // Should be <70% for this scenario
    });
  });

  describe('Edge Cases', () => {
    it('should handle scraping failures gracefully', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: false,
        error: 'Scraping failed',
        content: ''
      });

      const result = await costOptimizer.scrapeWithMaxCostOptimization('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Scraping failed');
    });

    it('should handle empty or null content', () => {
      const emptyContents = ['', null, undefined, '   ', '\n\n\n'];

      emptyContents.forEach(content => {
        const isGoodEnough = costOptimizer.isGoodEnoughContent(content as string);
        expect(isGoodEnough).toBe(false);
      });
    });

    it('should handle malformed URLs', async () => {
      const malformedUrls = [
        'not-a-url',
        'http://',
        'https://',
        'ftp://example.com'
      ];

      for (const url of malformedUrls) {
        const categorized = costOptimizer.categorizeUrlsByPattern([url]);
        expect(categorized.intelligent).toContain(url); // Should default to intelligent
      }
    });
  });
});
