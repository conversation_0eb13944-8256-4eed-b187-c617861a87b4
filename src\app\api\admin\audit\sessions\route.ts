/**
 * Admin User Sessions API Endpoint
 * Manages admin user sessions for security monitoring
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase';
import { SessionQueryOptions, SessionResponse, AdminUserSession, DbAdminUserSession } from '@/lib/types/audit';
import { log } from '@/lib/logging/logger';

/**
 * GET /api/admin/audit/sessions
 * Get admin user sessions with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const queryOptions: SessionQueryOptions = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: Math.min(parseInt(searchParams.get('limit') || '50'), 100),
      userId: searchParams.get('userId') || undefined,
      status: searchParams.get('status') as any,
      isSuspicious: searchParams.get('isSuspicious') === 'true' ? true : 
                   searchParams.get('isSuspicious') === 'false' ? false : undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      ipAddress: searchParams.get('ipAddress') || undefined
    };

    const page = queryOptions.page || 1;
    const limit = queryOptions.limit || 50;
    const offset = (page - 1) * limit;

    let query = supabaseAdmin
      .from('admin_user_sessions')
      .select('*', { count: 'exact' })
      .order('login_time', { ascending: false });

    // Apply filters
    if (queryOptions.userId) {
      query = query.eq('user_id', queryOptions.userId);
    }
    if (queryOptions.status) {
      query = query.eq('status', queryOptions.status);
    }
    if (queryOptions.isSuspicious !== undefined) {
      query = query.eq('is_suspicious', queryOptions.isSuspicious);
    }
    if (queryOptions.dateFrom) {
      query = query.gte('login_time', queryOptions.dateFrom);
    }
    if (queryOptions.dateTo) {
      query = query.lte('login_time', queryOptions.dateTo);
    }
    if (queryOptions.ipAddress) {
      query = query.eq('ip_address', queryOptions.ipAddress);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: sessions, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch sessions: ${error.message}`);
    }

    const transformedSessions = sessions?.map(session => transformDbSessionToSession(session)) || [];

    const response: SessionResponse = {
      sessions: transformedSessions,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };

    log.admin('sessions_retrieved', `Retrieved ${transformedSessions.length} admin sessions`, {
      page,
      limit,
      total: count || 0
    });

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    log.error('Error fetching admin sessions', error as Error, {
      component: 'sessions-api',
      operation: 'get_sessions'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch admin sessions' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/audit/sessions
 * Create or update an admin user session
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const sessionData = await request.json();

    // Validate required fields
    if (!sessionData.userId || !sessionData.sessionId) {
      return NextResponse.json(
        { success: false, error: 'userId and sessionId are required' },
        { status: 400 }
      );
    }

    // Add request metadata
    const ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent');

    const sessionRecord: Omit<DbAdminUserSession, 'id' | 'created_at' | 'updated_at'> = {
      user_id: sessionData.userId,
      session_id: sessionData.sessionId,
      login_time: sessionData.loginTime || new Date().toISOString(),
      logout_time: sessionData.logoutTime || null,
      last_activity: sessionData.lastActivity || new Date().toISOString(),
      ip_address: sessionData.ipAddress || ipAddress,
      user_agent: sessionData.userAgent || userAgent,
      login_method: sessionData.loginMethod || 'api_key',
      status: sessionData.status || 'active',
      termination_reason: sessionData.terminationReason || null,
      is_suspicious: sessionData.isSuspicious || false,
      failed_attempts: sessionData.failedAttempts || 0,
      security_alerts: sessionData.securityAlerts || null
    };

    // Check if session already exists
    const { data: existingSession } = await supabaseAdmin
      .from('admin_user_sessions')
      .select('id')
      .eq('session_id', sessionData.sessionId)
      .single();

    let result;
    if (existingSession) {
      // Update existing session
      const { data, error } = await supabaseAdmin
        .from('admin_user_sessions')
        .update({
          ...sessionRecord,
          updated_at: new Date().toISOString()
        })
        .eq('session_id', sessionData.sessionId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update session: ${error.message}`);
      }
      result = data;
    } else {
      // Create new session
      const { data, error } = await supabaseAdmin
        .from('admin_user_sessions')
        .insert([sessionRecord])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create session: ${error.message}`);
      }
      result = data;
    }

    log.admin('session_managed', `Admin session ${existingSession ? 'updated' : 'created'}`, {
      sessionId: sessionData.sessionId,
      userId: sessionData.userId,
      action: existingSession ? 'update' : 'create'
    });

    return NextResponse.json({
      success: true,
      data: transformDbSessionToSession(result)
    });

  } catch (error) {
    log.error('Error managing admin session', error as Error, {
      component: 'sessions-api',
      operation: 'manage_session'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to manage admin session' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/audit/sessions
 * Terminate admin user sessions
 */
export async function DELETE(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const userId = searchParams.get('userId');
    const terminateAll = searchParams.get('terminateAll') === 'true';
    const reason = searchParams.get('reason') || 'Manual termination';

    if (!sessionId && !userId && !terminateAll) {
      return NextResponse.json(
        { success: false, error: 'sessionId, userId, or terminateAll=true is required' },
        { status: 400 }
      );
    }

    let query = supabaseAdmin
      .from('admin_user_sessions')
      .update({
        status: 'terminated',
        logout_time: new Date().toISOString(),
        termination_reason: reason,
        updated_at: new Date().toISOString()
      });

    if (sessionId) {
      query = query.eq('session_id', sessionId);
    } else if (userId) {
      query = query.eq('user_id', userId).eq('status', 'active');
    } else if (terminateAll) {
      query = query.eq('status', 'active');
    }

    const { data, error } = await query.select();

    if (error) {
      throw new Error(`Failed to terminate sessions: ${error.message}`);
    }

    const terminatedCount = data?.length || 0;

    log.admin('sessions_terminated', `Terminated ${terminatedCount} admin sessions`, {
      sessionId,
      userId,
      terminateAll,
      reason,
      terminatedCount
    });

    return NextResponse.json({
      success: true,
      data: {
        terminatedSessions: terminatedCount,
        message: `Terminated ${terminatedCount} session${terminatedCount !== 1 ? 's' : ''}`
      }
    });

  } catch (error) {
    log.error('Error terminating admin sessions', error as Error, {
      component: 'sessions-api',
      operation: 'terminate_sessions'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to terminate admin sessions' },
      { status: 500 }
    );
  }
}

/**
 * Transform database session to application format
 */
function transformDbSessionToSession(dbSession: DbAdminUserSession): AdminUserSession {
  return {
    id: dbSession.id,
    userId: dbSession.user_id,
    sessionId: dbSession.session_id,
    loginTime: new Date(dbSession.login_time),
    logoutTime: dbSession.logout_time ? new Date(dbSession.logout_time) : undefined,
    lastActivity: new Date(dbSession.last_activity),
    ipAddress: dbSession.ip_address,
    userAgent: dbSession.user_agent,
    loginMethod: dbSession.login_method,
    status: dbSession.status,
    terminationReason: dbSession.termination_reason,
    isSuspicious: dbSession.is_suspicious,
    failedAttempts: dbSession.failed_attempts,
    securityAlerts: dbSession.security_alerts,
    createdAt: new Date(dbSession.created_at),
    updatedAt: new Date(dbSession.updated_at)
  };
}
