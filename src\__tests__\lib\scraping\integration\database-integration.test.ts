/**
 * Integration Tests for Database Integration
 * Tests data persistence, schema validation, transaction handling, and data integrity
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ScrapedDataRecord, EnhancedScrapeResult } from '@/lib/scraping/types';

// Mock Supabase client with proper chaining
const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  single: jest.fn().mockResolvedValue({ data: null, error: null }),
  rpc: jest.fn().mockResolvedValue({ data: null, error: null })
};

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabase)
}));

// Mock environment variables
Object.defineProperty(process, 'env', {
  value: {
    ...process.env,
    NEXT_PUBLIC_SUPABASE_URL: 'https://test.supabase.co',
    SUPABASE_SERVICE_ROLE_KEY: 'test-service-key'
  }
});

describe('Database Integration for Scraped Content', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Data Persistence', () => {
    it('should store scraped content with all metadata', async () => {
      const scrapedData: ScrapedDataRecord = {
        url: 'https://example.com',
        content: 'Test content with markdown formatting',
        mediaAssets: {
          favicon: ['https://example.com/favicon.ico'],
          ogImages: [{
            type: 'og:image',
            url: 'https://example.com/og-image.jpg',
            priority: 1
          }],
          screenshot: {
            success: true,
            screenshot: 'base64-screenshot-data',
            metadata: {
              width: 1200,
              height: 800,
              fullPage: false,
              capturedAt: new Date().toISOString()
            },
            timestamp: new Date().toISOString()
          }
        },
        additionalPages: [{
          success: true,
          content: 'Pricing page content',
          url: 'https://example.com/pricing',
          metadata: {
            pageType: 'pricing',
            creditsUsed: 1
          }
        }],
        contentAnalysis: {
          needsEnhancedScraping: false,
          confidence: 0.9,
          scenario: 'High quality content'
        },
        costAnalysis: {
          creditsUsed: 2,
          estimatedSavings: 3,
          optimizationStrategy: 'Pattern-based optimization'
        },
        scrapedAt: new Date().toISOString(),
        storedAt: new Date().toISOString()
      };

      mockSupabase.insert.mockResolvedValue({
        data: { id: 1, ...scrapedData },
        error: null
      });

      // Simulate storing data
      const result = await mockSupabase
        .from('scraped_content')
        .insert(scrapedData);

      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(mockSupabase.from).toHaveBeenCalledWith('scraped_content');
      expect(mockSupabase.insert).toHaveBeenCalledWith(scrapedData);
    });

    it('should handle JSONB fields correctly', async () => {
      const complexData = {
        url: 'https://example.com',
        content: 'Test content',
        media_assets: {
          favicon: ['https://example.com/favicon.ico'],
          ogImages: [
            {
              type: 'og:image',
              url: 'https://example.com/image1.jpg',
              priority: 1,
              metadata: {
                width: 1200,
                height: 630,
                format: 'jpeg'
              }
            }
          ]
        },
        additional_pages: [
          {
            success: true,
            content: 'Additional page content',
            url: 'https://example.com/page2',
            metadata: {
              pageType: 'features',
              creditsUsed: 1,
              extractedAt: new Date().toISOString()
            }
          }
        ],
        scraped_at: new Date().toISOString(),
        stored_at: new Date().toISOString()
      };

      mockSupabase.insert.mockResolvedValue({
        data: { id: 1, ...complexData },
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .insert(complexData);

      expect(result.data).toBeDefined();
      expect(result.data.media_assets).toEqual(complexData.media_assets);
      expect(result.data.additional_pages).toEqual(complexData.additional_pages);
    });

    it('should handle database constraints and validation', async () => {
      const invalidData = {
        // Missing required url field
        content: 'Test content',
        scraped_at: new Date().toISOString()
      };

      mockSupabase.insert.mockResolvedValue({
        data: null,
        error: {
          code: '23502',
          message: 'null value in column "url" violates not-null constraint'
        }
      });

      const result = await mockSupabase
        .from('scraped_content')
        .insert(invalidData);

      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe('23502');
    });
  });

  describe('Data Retrieval', () => {
    it('should retrieve scraped content by URL', async () => {
      const mockData = {
        id: 1,
        url: 'https://example.com',
        content: 'Retrieved content',
        media_assets: {
          favicon: ['https://example.com/favicon.ico']
        },
        scraped_at: new Date().toISOString()
      };

      mockSupabase.single.mockResolvedValue({
        data: mockData,
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .select('*')
        .eq('url', 'https://example.com')
        .single();

      expect(result.data).toEqual(mockData);
      expect(mockSupabase.eq).toHaveBeenCalledWith('url', 'https://example.com');
    });

    it('should retrieve recent scraped content with pagination', async () => {
      const mockData = [
        {
          id: 1,
          url: 'https://example1.com',
          content: 'Content 1',
          scraped_at: new Date().toISOString()
        },
        {
          id: 2,
          url: 'https://example2.com',
          content: 'Content 2',
          scraped_at: new Date().toISOString()
        }
      ];

      mockSupabase.single.mockResolvedValue({
        data: mockData,
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .select('*')
        .order('scraped_at', { ascending: false })
        .limit(10);

      expect(mockSupabase.order).toHaveBeenCalledWith('scraped_at', { ascending: false });
      expect(mockSupabase.limit).toHaveBeenCalledWith(10);
    });

    it('should handle missing records gracefully', async () => {
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: {
          code: 'PGRST116',
          message: 'The result contains 0 rows'
        }
      });

      const result = await mockSupabase
        .from('scraped_content')
        .select('*')
        .eq('url', 'https://nonexistent.com')
        .single();

      expect(result.data).toBeNull();
      expect(result.error.code).toBe('PGRST116');
    });
  });

  describe('Data Updates', () => {
    it('should update existing scraped content', async () => {
      const updateData = {
        content: 'Updated content',
        updated_at: new Date().toISOString()
      };

      mockSupabase.single.mockResolvedValue({
        data: { id: 1, ...updateData },
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .update(updateData)
        .eq('id', 1)
        .single();

      expect(result.data).toBeDefined();
      expect(mockSupabase.update).toHaveBeenCalledWith(updateData);
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', 1);
    });

    it('should handle concurrent updates with optimistic locking', async () => {
      const updateData = {
        content: 'Updated content',
        version: 2,
        updated_at: new Date().toISOString()
      };

      mockSupabase.single.mockResolvedValue({
        data: null,
        error: {
          code: '23505',
          message: 'duplicate key value violates unique constraint'
        }
      });

      const result = await mockSupabase
        .from('scraped_content')
        .update(updateData)
        .eq('id', 1)
        .eq('version', 1) // Optimistic locking
        .single();

      expect(result.error).toBeDefined();
      expect(result.error.code).toBe('23505');
    });
  });

  describe('Data Deletion', () => {
    it('should delete scraped content by ID', async () => {
      mockSupabase.single.mockResolvedValue({
        data: { id: 1 },
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .delete()
        .eq('id', 1)
        .single();

      expect(result.data).toBeDefined();
      expect(mockSupabase.delete).toHaveBeenCalled();
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', 1);
    });

    it('should delete old scraped content in batches', async () => {
      const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago

      mockSupabase.single.mockResolvedValue({
        data: { count: 25 },
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .delete()
        .eq('scraped_at', `lt.${cutoffDate.toISOString()}`);

      expect(mockSupabase.delete).toHaveBeenCalled();
    });
  });

  describe('Transaction Handling', () => {
    it('should handle database transactions for complex operations', async () => {
      // Mock transaction-like behavior
      const transactionData = [
        {
          url: 'https://example.com',
          content: 'Main content',
          scraped_at: new Date().toISOString()
        },
        {
          url: 'https://example.com/pricing',
          content: 'Pricing content',
          parent_url: 'https://example.com',
          scraped_at: new Date().toISOString()
        }
      ];

      mockSupabase.insert.mockResolvedValue({
        data: transactionData,
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .insert(transactionData);

      expect(result.data).toEqual(transactionData);
      expect(result.error).toBeNull();
    });

    it('should rollback on transaction failure', async () => {
      mockSupabase.insert.mockResolvedValue({
        data: null,
        error: {
          code: '23505',
          message: 'Transaction failed'
        }
      });

      const result = await mockSupabase
        .from('scraped_content')
        .insert([
          { url: 'https://example.com', content: 'Content 1' },
          { url: 'https://example.com', content: 'Content 2' } // Duplicate URL
        ]);

      expect(result.error).toBeDefined();
      expect(result.data).toBeNull();
    });
  });

  describe('Performance and Indexing', () => {
    it('should use database indexes for efficient queries', async () => {
      mockSupabase.single.mockResolvedValue({
        data: [
          { url: 'https://example1.com', scraped_at: '2024-01-01' },
          { url: 'https://example2.com', scraped_at: '2024-01-02' }
        ],
        error: null
      });

      // Query that should use URL index
      const result = await mockSupabase
        .from('scraped_content')
        .select('url, scraped_at')
        .eq('url', 'https://example.com');

      expect(mockSupabase.eq).toHaveBeenCalledWith('url', 'https://example.com');
    });

    it('should handle large dataset queries efficiently', async () => {
      const mockLargeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        url: `https://example${i}.com`,
        content: `Content ${i}`,
        scraped_at: new Date().toISOString()
      }));

      mockSupabase.single.mockResolvedValue({
        data: mockLargeDataset.slice(0, 100), // Paginated result
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .select('*')
        .order('scraped_at', { ascending: false })
        .limit(100);

      expect(mockSupabase.limit).toHaveBeenCalledWith(100);
    });
  });

  describe('Data Integrity', () => {
    it('should maintain referential integrity', async () => {
      const parentData = {
        id: 1,
        url: 'https://example.com',
        content: 'Parent content'
      };

      const childData = {
        url: 'https://example.com/pricing',
        content: 'Child content',
        parent_id: 1
      };

      mockSupabase.insert
        .mockResolvedValueOnce({
          data: parentData,
          error: null
        })
        .mockResolvedValueOnce({
          data: childData,
          error: null
        });

      // Insert parent first
      const parentResult = await mockSupabase
        .from('scraped_content')
        .insert(parentData);

      // Then insert child with reference
      const childResult = await mockSupabase
        .from('scraped_content')
        .insert(childData);

      expect(parentResult.data).toBeDefined();
      expect(childResult.data).toBeDefined();
      expect(childResult.data.parent_id).toBe(1);
    });

    it('should validate data types and constraints', async () => {
      const invalidData = {
        url: 'not-a-valid-url',
        content: null, // Assuming content is required
        scraped_at: 'invalid-date'
      };

      mockSupabase.insert.mockResolvedValue({
        data: null,
        error: {
          code: '22007',
          message: 'invalid input syntax for type timestamp'
        }
      });

      const result = await mockSupabase
        .from('scraped_content')
        .insert(invalidData);

      expect(result.error).toBeDefined();
      expect(result.error.code).toBe('22007');
    });
  });

  describe('Backup and Recovery', () => {
    it('should support data export for backup', async () => {
      const mockExportData = [
        {
          id: 1,
          url: 'https://example1.com',
          content: 'Content 1',
          scraped_at: '2024-01-01'
        },
        {
          id: 2,
          url: 'https://example2.com',
          content: 'Content 2',
          scraped_at: '2024-01-02'
        }
      ];

      mockSupabase.single.mockResolvedValue({
        data: mockExportData,
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .select('*')
        .order('id');

      expect(result.data).toEqual(mockExportData);
    });

    it('should support data import for recovery', async () => {
      const importData = [
        {
          url: 'https://restored1.com',
          content: 'Restored content 1',
          scraped_at: '2024-01-01'
        },
        {
          url: 'https://restored2.com',
          content: 'Restored content 2',
          scraped_at: '2024-01-02'
        }
      ];

      mockSupabase.insert.mockResolvedValue({
        data: importData,
        error: null
      });

      const result = await mockSupabase
        .from('scraped_content')
        .insert(importData);

      expect(result.data).toEqual(importData);
      expect(result.error).toBeNull();
    });
  });
});
