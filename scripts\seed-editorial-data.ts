#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function seedEditorialData() {
  console.log('🌱 Seeding editorial data...');

  try {
    // Get some existing tools
    const { data: tools, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, website, description')
      .limit(5);

    if (toolsError) {
      console.error('Error fetching tools:', toolsError);
      return;
    }

    if (!tools || tools.length === 0) {
      console.log('No tools found in database. Cannot create editorial reviews.');
      return;
    }

    console.log(`Found ${tools.length} tools. Creating editorial reviews...`);

    // Create some editorial reviews
    const editorialReviews = [
      {
        tool_id: tools[0].id,
        reviewed_by: 'admin',
        review_status: 'pending',
        review_date: new Date().toISOString().split('T')[0],
        review_notes: 'Initial review for AI-generated content',
        quality_score: 8
      },
      {
        tool_id: tools[1]?.id,
        reviewed_by: 'admin',
        review_status: 'approved',
        review_date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
        featured_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
        review_notes: 'Approved after review - high quality content',
        editorial_text: 'was manually vetted by our editorial team and was first featured on ' + new Date(Date.now() - 86400000).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }),
        quality_score: 9
      },
      {
        tool_id: tools[2]?.id,
        reviewed_by: 'admin',
        review_status: 'needs_revision',
        review_date: new Date(Date.now() - 172800000).toISOString().split('T')[0], // 2 days ago
        review_notes: 'Content needs improvement - missing key features section',
        quality_score: 6
      }
    ].filter(review => review.tool_id); // Filter out any undefined tool_ids

    // Insert editorial reviews
    const { data: insertedReviews, error: insertError } = await supabase
      .from('editorial_reviews')
      .insert(editorialReviews)
      .select();

    if (insertError) {
      console.error('Error inserting editorial reviews:', insertError);
      return;
    }

    console.log(`✅ Created ${insertedReviews.length} editorial reviews`);

    // Create some AI generation jobs
    const aiJobs = [
      {
        tool_id: tools[3]?.id,
        job_type: 'generate',
        status: 'completed',
        progress: 100,
        ai_responses: {
          content: 'This is a comprehensive AI tool that provides advanced features for productivity and automation. It offers an intuitive interface with powerful capabilities that help users streamline their workflows.',
          description: 'AI-powered productivity tool with advanced automation features'
        }
      },
      {
        tool_id: tools[4]?.id,
        job_type: 'generate',
        status: 'completed',
        progress: 100,
        ai_responses: {
          content: 'An innovative AI solution designed for modern businesses. This tool combines cutting-edge technology with user-friendly design to deliver exceptional results.',
          description: 'Modern AI business solution with cutting-edge technology'
        }
      }
    ].filter(job => job.tool_id); // Filter out any undefined tool_ids

    if (aiJobs.length > 0) {
      const { data: insertedJobs, error: jobsError } = await supabase
        .from('ai_generation_jobs')
        .insert(aiJobs)
        .select();

      if (jobsError) {
        console.error('Error inserting AI generation jobs:', jobsError);
      } else {
        console.log(`✅ Created ${insertedJobs.length} AI generation jobs`);
      }
    }

    // Create some prompt templates
    const promptTemplates = [
      {
        config_key: 'prompt_user_content_main',
        config_value: {
          name: 'Main Content Generation (User)',
          description: 'Primary user prompt for generating comprehensive tool descriptions',
          category: 'content',
          promptType: 'user',
          template: `You are ThePornDude, the irreverent and brutally honest reviewer of AI tools. Write a comprehensive review for {toolName} based on the following information:

URL: {toolUrl}
Scraped Content: {scrapedContent}

Write in your signature style:
- Be direct and no-bullshit
- Use humor and personality
- Focus on practical value
- Include both pros and cons
- Keep it engaging and readable

Structure:
1. Hook opening (2-3 sentences)
2. What it does (main features)
3. Why it matters (practical benefits)
4. The good stuff (pros)
5. The not-so-good (cons)
6. Bottom line verdict

Word count: 800-1200 words
Tone: Irreverent, honest, engaging`,
          variables: ['toolName', 'toolUrl', 'scrapedContent'],
          usage: 5
        },
        config_type: 'prompt_template',
        is_sensitive: false,
        is_active: true,
        description: 'Main content generation user prompt template',
        updated_by: 'admin'
      },
      {
        config_key: 'prompt_system_content_validation',
        config_value: {
          name: 'Content Validation (System)',
          description: 'System prompt for validating and formatting generated content',
          category: 'content',
          promptType: 'system',
          template: `You are a content validation system. Ensure the generated content meets these requirements:

VALIDATION RULES:
- Content must be between 800-1200 words
- Must include all required sections: hook, features, benefits, pros, cons, verdict
- Must maintain ThePornDude's irreverent tone
- Must include practical information about the tool
- Must not contain placeholder text or incomplete sections

FORMAT REQUIREMENTS:
- Use proper markdown formatting
- Include clear section headers
- Ensure readability with proper paragraph breaks
- Validate all variables have been replaced: {toolName}, {toolUrl}, {scrapedContent}

Return validation status and any formatting corrections needed.`,
          variables: ['toolName', 'toolUrl', 'scrapedContent'],
          validationRules: [
            'Word count between 800-1200',
            'All required sections present',
            'No placeholder text',
            'Proper markdown formatting',
            'Variables properly replaced'
          ],
          formatRequirements: 'Markdown with clear sections, proper headers, readable paragraphs',
          usage: 3
        },
        config_type: 'prompt_template',
        is_sensitive: false,
        is_active: true,
        description: 'Content validation system prompt template',
        updated_by: 'admin'
      },
      {
        config_key: 'prompt_user_features_detailed',
        config_value: {
          name: 'Features Analysis (User)',
          description: 'User prompt for analyzing and describing tool features',
          category: 'features',
          promptType: 'user',
          template: `Analyze the features of {toolName} and create a detailed breakdown:

Based on: {scrapedContent}

Create a structured features list that includes:
- Core functionality
- Advanced features
- Integration capabilities
- User experience elements
- Technical specifications

Format as clear, scannable bullet points with brief explanations.`,
          variables: ['toolName', 'scrapedContent'],
          usage: 3
        },
        config_type: 'prompt_template',
        is_sensitive: false,
        is_active: true,
        description: 'Features analysis user prompt template',
        updated_by: 'admin'
      },
      {
        config_key: 'prompt_system_features_validation',
        config_value: {
          name: 'Features Validation (System)',
          description: 'System prompt for validating feature analysis format',
          category: 'features',
          promptType: 'system',
          template: `Validate the features analysis meets these requirements:

VALIDATION RULES:
- Must include all 5 categories: Core functionality, Advanced features, Integration capabilities, User experience, Technical specifications
- Each feature must have a brief explanation
- Format must be clear bullet points
- No generic or placeholder descriptions
- Features must be specific to the tool

FORMAT REQUIREMENTS:
- Use markdown bullet points (-)
- Group features by category with headers
- Keep explanations concise but informative
- Ensure proper spacing and readability`,
          variables: ['toolName', 'scrapedContent'],
          validationRules: [
            'All 5 feature categories present',
            'Specific tool features only',
            'Clear bullet point format',
            'Brief explanations included',
            'No placeholder content'
          ],
          formatRequirements: 'Markdown bullet points grouped by category with headers',
          usage: 2
        },
        config_type: 'prompt_template',
        is_sensitive: false,
        is_active: true,
        description: 'Features validation system prompt template',
        updated_by: 'admin'
      }
    ];

    const { data: insertedPrompts, error: promptsError } = await supabase
      .from('system_configuration')
      .insert(promptTemplates)
      .select();

    if (promptsError) {
      console.error('Error inserting prompt templates:', promptsError);
    } else {
      console.log(`✅ Created ${insertedPrompts.length} prompt templates`);
    }

    console.log('🎉 Editorial data seeding completed successfully!');

  } catch (error) {
    console.error('Error seeding editorial data:', error);
    process.exit(1);
  }
}

// Run the seeding
seedEditorialData().catch(console.error);
