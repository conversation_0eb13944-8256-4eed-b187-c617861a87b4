/**
 * Audit Trail Types Tests
 * Simple test suite for audit trail type definitions and interfaces
 */

import {
  AuditAction,
  AuditResourceType,
  AuditStatus,
  AuditSeverity,
  AuditCategory,
  SessionStatus,
  LoginMethod,
  StatisticsPeriodType,
  AdminAuditLog,
  AdminUserSession,
  AuditLogRequest,
  AuditContext
} from '@/lib/types/audit';

describe('Audit Trail Types', () => {
  describe('Type Definitions', () => {
    it('should have valid AuditAction types', () => {
      const validActions: AuditAction[] = [
        'create_tool',
        'update_tool',
        'delete_tool',
        'create_category',
        'update_category',
        'delete_category',
        'create_user',
        'update_user',
        'delete_user',
        'update_config',
        'login',
        'logout',
        'failed_login'
      ];

      validActions.forEach(action => {
        expect(typeof action).toBe('string');
        expect(action.length).toBeGreaterThan(0);
      });
    });

    it('should have valid AuditResourceType types', () => {
      const validResourceTypes: AuditResourceType[] = [
        'tool',
        'category',
        'user',
        'config',
        'job',
        'session',
        'media',
        'submission',
        'review',
        'system'
      ];

      validResourceTypes.forEach(resourceType => {
        expect(typeof resourceType).toBe('string');
        expect(resourceType.length).toBeGreaterThan(0);
      });
    });

    it('should have valid AuditStatus types', () => {
      const validStatuses: AuditStatus[] = ['success', 'failed', 'partial'];

      validStatuses.forEach(status => {
        expect(typeof status).toBe('string');
        expect(['success', 'failed', 'partial']).toContain(status);
      });
    });

    it('should have valid AuditSeverity types', () => {
      const validSeverities: AuditSeverity[] = ['low', 'medium', 'high', 'critical'];

      validSeverities.forEach(severity => {
        expect(typeof severity).toBe('string');
        expect(['low', 'medium', 'high', 'critical']).toContain(severity);
      });
    });

    it('should have valid AuditCategory types', () => {
      const validCategories: AuditCategory[] = ['admin', 'security', 'data', 'system', 'user'];

      validCategories.forEach(category => {
        expect(typeof category).toBe('string');
        expect(['admin', 'security', 'data', 'system', 'user']).toContain(category);
      });
    });
  });

  describe('Interface Validation', () => {
    it('should create valid AdminAuditLog object', () => {
      const auditLog: AdminAuditLog = {
        id: 'test-id',
        action: 'create_tool',
        resourceType: 'tool',
        resourceId: 'tool-123',
        resourceName: 'Test Tool',
        performedBy: 'admin-user',
        userRole: 'admin',
        sessionId: 'session-123',
        requestId: 'request-123',
        httpMethod: 'POST',
        endpoint: '/api/admin/tools',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        actionDetails: { test: true },
        oldValues: { name: 'Old Name' },
        newValues: { name: 'New Name' },
        status: 'success',
        errorMessage: undefined,
        performedAt: new Date(),
        severity: 'medium',
        category: 'admin',
        tags: ['important'],
        retentionPeriod: '7 years',
        isSensitive: false,
        complianceFlags: ['GDPR']
      };

      expect(auditLog.id).toBe('test-id');
      expect(auditLog.action).toBe('create_tool');
      expect(auditLog.resourceType).toBe('tool');
      expect(auditLog.status).toBe('success');
      expect(auditLog.severity).toBe('medium');
      expect(auditLog.category).toBe('admin');
      expect(auditLog.performedAt).toBeInstanceOf(Date);
      expect(auditLog.isSensitive).toBe(false);
    });

    it('should create valid AdminUserSession object', () => {
      const session: AdminUserSession = {
        id: 'session-id',
        userId: 'user-123',
        sessionId: 'sess-456',
        loginTime: new Date(),
        logoutTime: undefined,
        lastActivity: new Date(),
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        loginMethod: 'api_key',
        status: 'active',
        terminationReason: undefined,
        isSuspicious: false,
        failedAttempts: 0,
        securityAlerts: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      expect(session.id).toBe('session-id');
      expect(session.userId).toBe('user-123');
      expect(session.sessionId).toBe('sess-456');
      expect(session.loginMethod).toBe('api_key');
      expect(session.status).toBe('active');
      expect(session.isSuspicious).toBe(false);
      expect(session.failedAttempts).toBe(0);
    });

    it('should create valid AuditLogRequest object', () => {
      const request: AuditLogRequest = {
        action: 'update_tool',
        resourceType: 'tool',
        resourceId: 'tool-456',
        resourceName: 'Updated Tool',
        performedBy: 'admin-user',
        userRole: 'admin',
        sessionId: 'session-789',
        requestId: 'request-789',
        httpMethod: 'PUT',
        endpoint: '/api/admin/tools/456',
        ipAddress: '********',
        userAgent: 'Chrome/91.0',
        actionDetails: { updated: true },
        oldValues: { status: 'draft' },
        newValues: { status: 'published' },
        status: 'success',
        severity: 'medium',
        category: 'admin',
        tags: ['update', 'publish'],
        isSensitive: false,
        complianceFlags: ['SOX']
      };

      expect(request.action).toBe('update_tool');
      expect(request.resourceType).toBe('tool');
      expect(request.performedBy).toBe('admin-user');
      expect(request.status).toBe('success');
      expect(request.httpMethod).toBe('PUT');
      expect(Array.isArray(request.tags)).toBe(true);
      expect(Array.isArray(request.complianceFlags)).toBe(true);
    });

    it('should create valid AuditContext object', () => {
      const context: AuditContext = {
        userId: 'user-123',
        userRole: 'admin',
        sessionId: 'session-456',
        requestId: 'request-789',
        ipAddress: '**********',
        userAgent: 'Safari/14.0'
      };

      expect(context.userId).toBe('user-123');
      expect(context.userRole).toBe('admin');
      expect(context.sessionId).toBe('session-456');
      expect(context.requestId).toBe('request-789');
      expect(context.ipAddress).toBe('**********');
      expect(context.userAgent).toBe('Safari/14.0');
    });
  });

  describe('Type Safety', () => {
    it('should enforce required fields in AuditLogRequest', () => {
      // This test ensures TypeScript compilation catches missing required fields
      const minimalRequest: AuditLogRequest = {
        action: 'create_category',
        resourceType: 'category',
        performedBy: 'admin-user'
      };

      expect(minimalRequest.action).toBe('create_category');
      expect(minimalRequest.resourceType).toBe('category');
      expect(minimalRequest.performedBy).toBe('admin-user');
    });

    it('should allow optional fields in interfaces', () => {
      const partialAuditLog: Partial<AdminAuditLog> = {
        id: 'partial-id',
        action: 'delete_user',
        resourceType: 'user',
        performedBy: 'admin-user',
        status: 'failed',
        errorMessage: 'User not found',
        performedAt: new Date(),
        severity: 'high',
        category: 'security',
        isSensitive: true
      };

      expect(partialAuditLog.id).toBe('partial-id');
      expect(partialAuditLog.action).toBe('delete_user');
      expect(partialAuditLog.status).toBe('failed');
      expect(partialAuditLog.severity).toBe('high');
      expect(partialAuditLog.isSensitive).toBe(true);
    });
  });

  describe('Enum-like Type Validation', () => {
    it('should validate SessionStatus values', () => {
      const validStatuses: SessionStatus[] = ['active', 'expired', 'terminated', 'suspicious'];
      
      validStatuses.forEach(status => {
        expect(['active', 'expired', 'terminated', 'suspicious']).toContain(status);
      });
    });

    it('should validate LoginMethod values', () => {
      const validMethods: LoginMethod[] = ['api_key', 'jwt', 'oauth'];
      
      validMethods.forEach(method => {
        expect(['api_key', 'jwt', 'oauth']).toContain(method);
      });
    });

    it('should validate StatisticsPeriodType values', () => {
      const validPeriods: StatisticsPeriodType[] = ['hour', 'day', 'week', 'month'];
      
      validPeriods.forEach(period => {
        expect(['hour', 'day', 'week', 'month']).toContain(period);
      });
    });
  });
});
