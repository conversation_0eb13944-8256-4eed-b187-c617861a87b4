-- Update system_configuration table to allow 'prompt_template' as a valid config_type
-- This enables the admin prompts management functionality

-- Drop the existing constraint
ALTER TABLE system_configuration DROP CONSTRAINT IF EXISTS system_configuration_config_type_check;

-- Add the updated constraint with 'prompt_template' included
ALTER TABLE system_configuration ADD CONSTRAINT system_configuration_config_type_check 
CHECK (config_type IN ('ai_provider', 'scraping', 'job_processing', 'system', 'security', 'prompt_template'));

-- Verify the constraint was updated
SELECT conname, consrc 
FROM pg_constraint 
WHERE conrelid = 'system_configuration'::regclass 
AND conname = 'system_configuration_config_type_check';
