/**
 * Audit Middleware
 * Automatic audit logging for admin API operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { adminAuditLogger } from './admin-audit-logger';
import { log } from '@/lib/logging/logger';
import {
  AuditAction,
  AuditResourceType,
  AuditContext,
  AuditSeverity
} from '@/lib/types/audit';

// Map HTTP methods and endpoints to audit actions
const AUDIT_ACTION_MAP: Record<string, Record<string, AuditAction>> = {
  'POST': {
    '/api/admin/tools': 'create_tool',
    '/api/admin/categories': 'create_category',
    '/api/admin/config': 'update_config',
    '/api/admin/config/import': 'import_config',
    '/api/admin/config/test-providers': 'test_provider',
    '/api/admin/jobs': 'start_job',
    '/api/admin/bulk/import': 'bulk_import',
    '/api/admin/editorial/approve': 'approve_submission',
    '/api/admin/editorial/reject': 'reject_submission',
    '/api/admin/media/upload': 'upload_media'
  },
  'PUT': {
    '/api/admin/tools': 'update_tool',
    '/api/admin/categories': 'update_category',
    '/api/admin/config': 'update_config'
  },
  'DELETE': {
    '/api/admin/tools': 'delete_tool',
    '/api/admin/categories': 'delete_category',
    '/api/admin/media': 'delete_media'
  },
  'PATCH': {
    '/api/admin/tools': 'update_tool',
    '/api/admin/categories': 'update_category'
  }
};

// Map endpoints to resource types
const RESOURCE_TYPE_MAP: Record<string, AuditResourceType> = {
  '/api/admin/tools': 'tool',
  '/api/admin/categories': 'category',
  '/api/admin/config': 'config',
  '/api/admin/jobs': 'job',
  '/api/admin/bulk': 'system',
  '/api/admin/editorial': 'submission',
  '/api/admin/media': 'media',
  '/api/admin/users': 'user'
};

// Endpoints that should be audited
const AUDITABLE_ENDPOINTS = [
  '/api/admin/tools',
  '/api/admin/categories',
  '/api/admin/config',
  '/api/admin/jobs',
  '/api/admin/bulk',
  '/api/admin/editorial',
  '/api/admin/media',
  '/api/admin/users'
];

export interface AuditMiddlewareOptions {
  enabled?: boolean;
  excludeEndpoints?: string[];
  includeRequestBody?: boolean;
  includeResponseBody?: boolean;
  sensitiveFields?: string[];
}

export class AuditMiddleware {
  private options: AuditMiddlewareOptions;

  constructor(options: AuditMiddlewareOptions = {}) {
    this.options = {
      enabled: true,
      excludeEndpoints: [],
      includeRequestBody: true,
      includeResponseBody: false,
      sensitiveFields: ['password', 'api_key', 'secret', 'token'],
      ...options
    };
  }

  /**
   * Middleware function to wrap API handlers with audit logging
   */
  withAudit<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
      if (!this.options.enabled) {
        return handler(request, ...args);
      }

      const startTime = Date.now();
      const requestId = this.generateRequestId();
      const context = await this.extractAuditContext(request, requestId);
      
      let response: NextResponse;
      let auditAction: AuditAction | null = null;
      let resourceType: AuditResourceType | null = null;
      let resourceInfo: { id?: string; name?: string } = {};

      try {
        // Check if endpoint should be audited
        if (!this.shouldAuditEndpoint(request.url)) {
          return handler(request, ...args);
        }

        // Extract audit information
        const auditInfo = this.extractAuditInfo(request);
        auditAction = auditInfo.action;
        resourceType = auditInfo.resourceType;

        // Get request body for audit details
        let requestBody: any = null;
        if (this.options.includeRequestBody && this.hasBody(request)) {
          try {
            requestBody = await this.cloneAndParseRequest(request);
            resourceInfo = this.extractResourceInfo(requestBody, auditAction);
          } catch (error) {
            log.warn('Failed to parse request body for audit', {
              component: 'audit-middleware',
              requestId,
              error: (error as Error).message
            });
          }
        }

        // Execute the handler
        response = await handler(request, ...args);

        // Log successful action
        if (auditAction && resourceType && context) {
          await this.logSuccessfulAction(
            auditAction,
            resourceType,
            context,
            {
              resourceInfo,
              requestBody: this.sanitizeData(requestBody),
              responseStatus: response.status,
              duration: Date.now() - startTime,
              endpoint: new URL(request.url).pathname,
              httpMethod: request.method
            }
          );
        }

        return response;

      } catch (error) {
        // Log failed action
        if (auditAction && resourceType && context) {
          await this.logFailedAction(
            auditAction,
            resourceType,
            context,
            error as Error,
            {
              resourceInfo,
              duration: Date.now() - startTime,
              endpoint: new URL(request.url).pathname,
              httpMethod: request.method
            }
          );
        }

        throw error;
      }
    };
  }

  /**
   * Extract audit context from request
   */
  private async extractAuditContext(request: NextRequest, requestId: string): Promise<AuditContext | null> {
    try {
      // Extract user information from headers or JWT
      const userId = request.headers.get('x-user-id') || 
                    request.headers.get('x-admin-user') || 
                    'system'; // Default for system operations

      const userRole = request.headers.get('x-user-role') || 'admin';
      const sessionId = request.headers.get('x-session-id') || this.generateSessionId();
      const ipAddress = this.getClientIP(request);
      const userAgent = request.headers.get('user-agent') || undefined;

      return {
        userId,
        userRole,
        sessionId,
        requestId,
        ipAddress,
        userAgent
      };
    } catch (error) {
      log.warn('Failed to extract audit context', {
        component: 'audit-middleware',
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Extract audit action and resource type from request
   */
  private extractAuditInfo(request: NextRequest): {
    action: AuditAction | null;
    resourceType: AuditResourceType | null;
  } {
    const url = new URL(request.url);
    const pathname = url.pathname;
    const method = request.method;

    // Find matching endpoint pattern
    let matchedEndpoint: string | null = null;
    for (const endpoint of Object.keys(RESOURCE_TYPE_MAP)) {
      if (pathname.startsWith(endpoint)) {
        matchedEndpoint = endpoint;
        break;
      }
    }

    if (!matchedEndpoint) {
      return { action: null, resourceType: null };
    }

    const action = AUDIT_ACTION_MAP[method]?.[matchedEndpoint] || null;
    const resourceType = RESOURCE_TYPE_MAP[matchedEndpoint] || null;

    return { action, resourceType };
  }

  /**
   * Extract resource information from request body
   */
  private extractResourceInfo(requestBody: any, action: AuditAction | null): { id?: string; name?: string } {
    if (!requestBody) return {};

    const info: { id?: string; name?: string } = {};

    // Extract ID from various possible fields
    if (requestBody.id) info.id = requestBody.id;
    if (requestBody.toolId) info.id = requestBody.toolId;
    if (requestBody.categoryId) info.id = requestBody.categoryId;

    // Extract name from various possible fields
    if (requestBody.name) info.name = requestBody.name;
    if (requestBody.title) info.name = requestBody.title;
    if (requestBody.slug) info.name = requestBody.slug;

    return info;
  }

  /**
   * Log successful action
   */
  private async logSuccessfulAction(
    action: AuditAction,
    resourceType: AuditResourceType,
    context: AuditContext,
    details: {
      resourceInfo: { id?: string; name?: string };
      requestBody?: any;
      responseStatus: number;
      duration: number;
      endpoint: string;
      httpMethod: string;
    }
  ): Promise<void> {
    try {
      await adminAuditLogger.logSuccess(action, resourceType, context, {
        resourceId: details.resourceInfo.id,
        resourceName: details.resourceInfo.name,
        actionDetails: {
          endpoint: details.endpoint,
          httpMethod: details.httpMethod,
          responseStatus: details.responseStatus,
          duration: details.duration,
          requestData: details.requestBody
        }
      });
    } catch (error) {
      log.error('Failed to log successful audit action', error as Error, {
        component: 'audit-middleware',
        action,
        resourceType
      });
    }
  }

  /**
   * Log failed action
   */
  private async logFailedAction(
    action: AuditAction,
    resourceType: AuditResourceType,
    context: AuditContext,
    error: Error,
    details: {
      resourceInfo: { id?: string; name?: string };
      duration: number;
      endpoint: string;
      httpMethod: string;
    }
  ): Promise<void> {
    try {
      const severity: AuditSeverity = this.determineSeverity(error, action);
      
      await adminAuditLogger.logFailure(action, resourceType, context, error.message, {
        resourceId: details.resourceInfo.id,
        resourceName: details.resourceInfo.name,
        severity,
        actionDetails: {
          endpoint: details.endpoint,
          httpMethod: details.httpMethod,
          duration: details.duration,
          errorStack: error.stack
        }
      });
    } catch (auditError) {
      log.error('Failed to log failed audit action', auditError as Error, {
        component: 'audit-middleware',
        action,
        resourceType,
        originalError: error.message
      });
    }
  }

  /**
   * Determine error severity based on error type and action
   */
  private determineSeverity(error: Error, action: AuditAction): AuditSeverity {
    // Security-related actions get higher severity
    if (action.includes('delete') || action.includes('config')) {
      return 'high';
    }

    // Authentication/authorization errors are critical
    if (error.message.toLowerCase().includes('unauthorized') || 
        error.message.toLowerCase().includes('forbidden')) {
      return 'critical';
    }

    // Validation errors are medium
    if (error.message.toLowerCase().includes('validation') ||
        error.message.toLowerCase().includes('invalid')) {
      return 'medium';
    }

    return 'medium';
  }

  /**
   * Check if endpoint should be audited
   */
  private shouldAuditEndpoint(url: string): boolean {
    const pathname = new URL(url).pathname;
    
    // Check if excluded
    if (this.options.excludeEndpoints?.some(excluded => pathname.startsWith(excluded))) {
      return false;
    }

    // Check if in auditable endpoints
    return AUDITABLE_ENDPOINTS.some(endpoint => pathname.startsWith(endpoint));
  }

  /**
   * Check if request has body
   */
  private hasBody(request: NextRequest): boolean {
    return ['POST', 'PUT', 'PATCH'].includes(request.method);
  }

  /**
   * Clone and parse request body
   */
  private async cloneAndParseRequest(request: NextRequest): Promise<any> {
    const cloned = request.clone();
    const text = await cloned.text();
    
    if (!text) return null;
    
    try {
      return JSON.parse(text);
    } catch {
      return { rawBody: text };
    }
  }

  /**
   * Sanitize sensitive data from objects
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') return data;

    const sanitized = { ...data };
    
    for (const field of this.options.sensitiveFields || []) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * Get client IP address
   */
  private getClientIP(request: NextRequest): string {
    return request.headers.get('x-forwarded-for')?.split(',')[0] ||
           request.headers.get('x-real-ip') ||
           request.headers.get('cf-connecting-ip') ||
           'unknown';
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Default middleware instance
export const auditMiddleware = new AuditMiddleware();
