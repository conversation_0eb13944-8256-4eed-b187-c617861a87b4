-- Manual Migration 002: Add Environment and Configuration Columns
-- Run this SQL manually in Supabase SQL Editor to add the missing columns

-- Step 1: Add environment column to system_configuration table
ALTER TABLE system_configuration 
ADD COLUMN IF NOT EXISTS environment VARCHAR(50) DEFAULT 'development';

-- Step 2: Add configuration column to store complete configuration objects
ALTER TABLE system_configuration 
ADD COLUMN IF NOT EXISTS configuration JSONB;

-- Step 3: Create index for environment column for better query performance
CREATE INDEX IF NOT EXISTS idx_system_configuration_environment 
ON system_configuration(environment);

-- Step 4: Create unique constraint for environment (one config per environment)
CREATE UNIQUE INDEX IF NOT EXISTS idx_system_configuration_environment_unique 
ON system_configuration(environment) 
WHERE configuration IS NOT NULL;

-- Step 5: Insert default configuration for current environment if not exists
INSERT INTO system_configuration (
    config_key, 
    config_value, 
    config_type, 
    environment, 
    configuration,
    description
) VALUES (
    'default_environment_config',
    '{}'::jsonb,
    'system',
    'development',
    jsonb_build_object(
        'aiGeneration', jsonb_build_object(
            'providers', jsonb_build_object(
                'openai', jsonb_build_object(
                    'enabled', true,
                    'model', 'gpt-4o-2024-11-20',
                    'maxTokens', 16384,
                    'temperature', 0.7,
                    'timeout', 60000,
                    'priority', 1
                ),
                'openrouter', jsonb_build_object(
                    'enabled', true,
                    'model', 'google/gemini-2.5-pro-preview',
                    'maxTokens', 65536,
                    'temperature', 0.7,
                    'implicitCaching', true,
                    'timeout', 120000,
                    'priority', 2
                )
            ),
            'modelSelection', jsonb_build_object(
                'strategy', 'auto',
                'fallbackOrder', jsonb_build_array('openai', 'openrouter'),
                'costThreshold', 0.01,
                'qualityThreshold', 0.8
            ),
            'contentGeneration', jsonb_build_object(
                'autoApproval', false,
                'qualityThreshold', 0.8,
                'editorialReviewRequired', true,
                'maxRetries', 3,
                'timeoutSeconds', 300
            )
        ),
        'scraping', jsonb_build_object(
            'scrapeDoConfig', jsonb_build_object(
                'enabled', true,
                'timeout', 30000,
                'retryAttempts', 3,
                'costOptimization', jsonb_build_object(
                    'enabled', true,
                    'neverEnhancePatterns', jsonb_build_array('simple-landing', 'basic-info'),
                    'alwaysEnhancePatterns', jsonb_build_array('complex-saas', 'feature-rich')
                )
            ),
            'mediaExtraction', jsonb_build_object(
                'ogImageExtraction', true,
                'faviconCollection', true,
                'screenshotFallback', true,
                'persistentStorage', true
            )
        ),
        'system', jsonb_build_object(
            'contentQualityThreshold', 0.7,
            'autoApprovalEnabled', false,
            'debugMode', false,
            'maintenanceMode', false,
            'security', jsonb_build_object(
                'apiKeyRotationDays', 90,
                'sessionTimeoutMinutes', 60,
                'maxLoginAttempts', 5,
                'auditLogging', true
            ),
            'performance', jsonb_build_object(
                'cacheEnabled', true,
                'cacheTTL', 3600,
                'rateLimiting', true,
                'requestsPerMinute', 100
            )
        ),
        'editorial', jsonb_build_object(
            'workflow', jsonb_build_object(
                'autoAssignment', false,
                'reviewTimeoutHours', 24,
                'escalationEnabled', true,
                'qualityChecks', true
            ),
            'contentStandards', jsonb_build_object(
                'minDescriptionLength', 50,
                'maxDescriptionLength', 500,
                'requiredFields', jsonb_build_array('name', 'description', 'url'),
                'bannedWords', jsonb_build_array()
            )
        )
    ),
    'Default configuration for environment-based configuration management'
) ON CONFLICT (config_key) DO UPDATE SET
    environment = EXCLUDED.environment,
    configuration = EXCLUDED.configuration,
    updated_at = CURRENT_TIMESTAMP;

-- Step 6: Update existing records to have environment set
UPDATE system_configuration 
SET environment = 'development'
WHERE environment IS NULL;

-- Step 7: Verify the migration worked
SELECT 
    config_key, 
    config_type, 
    environment,
    CASE 
        WHEN configuration IS NOT NULL THEN 'Has configuration data'
        ELSE 'No configuration data'
    END as config_status
FROM system_configuration 
WHERE config_key = 'default_environment_config';

-- Step 8: Show column structure to confirm
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'system_configuration' 
ORDER BY ordinal_position;
