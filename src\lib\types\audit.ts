/**
 * Audit Trail Types and Interfaces
 * Comprehensive type definitions for admin audit logging system
 */

// Core audit action types
export type AuditAction = 
  // Tool operations
  | 'create_tool' | 'update_tool' | 'delete_tool' | 'publish_tool' | 'archive_tool'
  // Category operations
  | 'create_category' | 'update_category' | 'delete_category'
  // User operations
  | 'create_user' | 'update_user' | 'delete_user' | 'reset_password' | 'update_permissions'
  // Configuration operations
  | 'update_config' | 'import_config' | 'export_config' | 'test_provider'
  // System operations
  | 'start_job' | 'stop_job' | 'restart_service' | 'clear_cache'
  // Security operations
  | 'login' | 'logout' | 'failed_login' | 'session_expired'
  // Bulk operations
  | 'bulk_import' | 'bulk_export' | 'bulk_update' | 'bulk_delete'
  // Review operations
  | 'approve_submission' | 'reject_submission' | 'feature_tool'
  // Media operations
  | 'upload_media' | 'delete_media' | 'update_media';

// Resource types that can be audited
export type AuditResourceType = 
  | 'tool' | 'category' | 'user' | 'config' | 'job' | 'session' 
  | 'media' | 'submission' | 'review' | 'system';

// Audit log status
export type AuditStatus = 'success' | 'failed' | 'partial';

// Audit severity levels
export type AuditSeverity = 'low' | 'medium' | 'high' | 'critical';

// Audit categories
export type AuditCategory = 'admin' | 'security' | 'data' | 'system' | 'user';

// Session status
export type SessionStatus = 'active' | 'expired' | 'terminated' | 'suspicious';

// Login methods
export type LoginMethod = 'api_key' | 'jwt' | 'oauth';

// Statistics period types
export type StatisticsPeriodType = 'hour' | 'day' | 'week' | 'month';

// Database interfaces (snake_case for DB compatibility)
export interface DbAdminAuditLog {
  id: string;
  action: AuditAction;
  resource_type: AuditResourceType;
  resource_id?: string;
  resource_name?: string;
  performed_by: string;
  user_role?: string;
  session_id?: string;
  request_id?: string;
  http_method?: string;
  endpoint?: string;
  ip_address?: string;
  user_agent?: string;
  action_details?: Record<string, any>;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  status: AuditStatus;
  error_message?: string;
  performed_at: string;
  severity: AuditSeverity;
  category: AuditCategory;
  tags?: string[];
  retention_period?: string;
  is_sensitive: boolean;
  compliance_flags?: string[];
}

export interface DbAdminUserSession {
  id: string;
  user_id: string;
  session_id: string;
  login_time: string;
  logout_time?: string;
  last_activity: string;
  ip_address: string;
  user_agent?: string;
  login_method: LoginMethod;
  status: SessionStatus;
  termination_reason?: string;
  is_suspicious: boolean;
  failed_attempts: number;
  security_alerts?: string[];
  created_at: string;
  updated_at: string;
}

export interface DbAuditLogStatistics {
  id: string;
  period_start: string;
  period_end: string;
  period_type: StatisticsPeriodType;
  total_actions: number;
  successful_actions: number;
  failed_actions: number;
  action_breakdown?: Record<string, number>;
  resource_breakdown?: Record<string, number>;
  user_breakdown?: Record<string, number>;
  suspicious_activities: number;
  security_alerts: number;
  failed_logins: number;
  avg_response_time?: number;
  peak_activity_hour?: number;
  created_at: string;
}

// Application interfaces (camelCase for TypeScript)
export interface AdminAuditLog {
  id: string;
  action: AuditAction;
  resourceType: AuditResourceType;
  resourceId?: string;
  resourceName?: string;
  performedBy: string;
  userRole?: string;
  sessionId?: string;
  requestId?: string;
  httpMethod?: string;
  endpoint?: string;
  ipAddress?: string;
  userAgent?: string;
  actionDetails?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  status: AuditStatus;
  errorMessage?: string;
  performedAt: Date;
  severity: AuditSeverity;
  category: AuditCategory;
  tags?: string[];
  retentionPeriod?: string;
  isSensitive: boolean;
  complianceFlags?: string[];
}

export interface AdminUserSession {
  id: string;
  userId: string;
  sessionId: string;
  loginTime: Date;
  logoutTime?: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent?: string;
  loginMethod: LoginMethod;
  status: SessionStatus;
  terminationReason?: string;
  isSuspicious: boolean;
  failedAttempts: number;
  securityAlerts?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface AuditLogStatistics {
  id: string;
  periodStart: Date;
  periodEnd: Date;
  periodType: StatisticsPeriodType;
  totalActions: number;
  successfulActions: number;
  failedActions: number;
  actionBreakdown?: Record<string, number>;
  resourceBreakdown?: Record<string, number>;
  userBreakdown?: Record<string, number>;
  suspiciousActivities: number;
  securityAlerts: number;
  failedLogins: number;
  avgResponseTime?: number;
  peakActivityHour?: number;
  createdAt: Date;
}

// Request/Response interfaces
export interface AuditLogRequest {
  action: AuditAction;
  resourceType: AuditResourceType;
  resourceId?: string;
  resourceName?: string;
  performedBy: string;
  userRole?: string;
  sessionId?: string;
  requestId?: string;
  httpMethod?: string;
  endpoint?: string;
  ipAddress?: string;
  userAgent?: string;
  actionDetails?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  status?: AuditStatus;
  errorMessage?: string;
  severity?: AuditSeverity;
  category?: AuditCategory;
  tags?: string[];
  isSensitive?: boolean;
  complianceFlags?: string[];
}

export interface AuditLogQueryOptions {
  page?: number;
  limit?: number;
  action?: AuditAction;
  resourceType?: AuditResourceType;
  resourceId?: string;
  performedBy?: string;
  status?: AuditStatus;
  severity?: AuditSeverity;
  category?: AuditCategory;
  dateFrom?: string;
  dateTo?: string;
  searchTerm?: string;
  tags?: string[];
  includeDetails?: boolean;
  sortBy?: 'performed_at' | 'action' | 'resource_type' | 'performed_by';
  sortOrder?: 'asc' | 'desc';
}

export interface AuditLogResponse {
  logs: AdminAuditLog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface SessionQueryOptions {
  page?: number;
  limit?: number;
  userId?: string;
  status?: SessionStatus;
  isSuspicious?: boolean;
  dateFrom?: string;
  dateTo?: string;
  ipAddress?: string;
}

export interface SessionResponse {
  sessions: AdminUserSession[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface AuditStatisticsRequest {
  periodType: StatisticsPeriodType;
  periodStart: string;
  periodEnd: string;
  includeBreakdowns?: boolean;
}

export interface AuditDashboardData {
  summary: {
    totalActions: number;
    successfulActions: number;
    failedActions: number;
    suspiciousActivities: number;
    activeSessions: number;
  };
  recentActivity: AdminAuditLog[];
  topUsers: Array<{
    userId: string;
    actionCount: number;
    lastAction: Date;
  }>;
  actionBreakdown: Record<string, number>;
  resourceBreakdown: Record<string, number>;
  securityAlerts: Array<{
    type: string;
    message: string;
    timestamp: Date;
    severity: AuditSeverity;
  }>;
  performanceMetrics: {
    avgResponseTime: number;
    peakActivityHour: number;
    errorRate: number;
  };
}

// Export utility type for audit context
export interface AuditContext {
  userId: string;
  userRole?: string;
  sessionId?: string;
  requestId?: string;
  ipAddress?: string;
  userAgent?: string;
}
