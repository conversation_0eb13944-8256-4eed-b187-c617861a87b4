'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { JobMonitoringDashboard } from '@/components/admin/JobMonitoringDashboard';
import { JobMetricsCards } from '@/components/admin/JobMetricsCards';
import { JobListTable } from '@/components/admin/JobListTable';
import { JobDetailsModal } from '@/components/admin/JobDetailsModal';
import { useJobMonitoring } from '@/hooks/useJobMonitoring';
import { useWebSocketConnection } from '@/hooks/useWebSocketConnection';
import { Job, JobStatus, JobType } from '@/lib/jobs/types';
import { validateAdminAccess } from '@/lib/auth';

interface JobMonitoringPageProps {}

interface JobFilters {
  status?: JobStatus;
  type?: JobType;
  dateRange?: {
    start: Date;
    end: Date;
  };
  toolId?: string;
  search?: string;
}

interface JobMetrics {
  totalJobs: number;
  activeJobs: number;
  queuedJobs: number;
  completedToday: number;
  failedJobs: number;
  successRate: number;
  averageProcessingTime: number;
  queueHealth: 'healthy' | 'warning' | 'error';
}

/**
 * Job Monitoring Dashboard Page
 * 
 * Provides comprehensive real-time monitoring of the background job processing system.
 * Features:
 * - Real-time job status updates via WebSocket
 * - Interactive job metrics and performance indicators
 * - Advanced filtering and search capabilities
 * - Detailed job inspection and management
 * - Bulk job operations
 * - System health monitoring
 */
export default function JobMonitoringPage(): React.JSX.Element {
  // State management
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [filters, setFilters] = useState<JobFilters>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Custom hooks for job monitoring
  const {
    jobs,
    metrics,
    refreshJobs,
    refreshMetrics,
    isRefreshing
  } = useJobMonitoring(filters);

  const {
    isConnected,
    connectionStatus,
    subscribe,
    unsubscribe
  } = useWebSocketConnection();

  // Authorization check
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authorized = await validateAdminAccess();
        setIsAuthorized(authorized);
        if (!authorized) {
          setError('Unauthorized access. Admin privileges required.');
        }
      } catch (err) {
        setError('Failed to validate admin access');
        console.error('Auth validation error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // WebSocket event handlers
  const handleJobUpdate = useCallback((jobUpdate: any) => {
    console.log('📡 Received job update:', jobUpdate);
    // Refresh jobs list when updates are received
    refreshJobs();
    refreshMetrics();
  }, [refreshJobs, refreshMetrics]);

  const handleJobCreated = useCallback((job: Job) => {
    console.log('🎯 New job created:', job.id);
    refreshJobs();
    refreshMetrics();
  }, [refreshJobs, refreshMetrics]);

  const handleJobCompleted = useCallback((job: Job) => {
    console.log('✅ Job completed:', job.id);
    refreshJobs();
    refreshMetrics();
  }, [refreshJobs, refreshMetrics]);

  const handleJobFailed = useCallback((job: Job) => {
    console.log('❌ Job failed:', job.id);
    refreshJobs();
    refreshMetrics();
  }, [refreshJobs, refreshMetrics]);

  // Filter handlers
  const handleFilterChange = useCallback((newFilters: Partial<JobFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const handleClearFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Job action handlers
  const handleJobSelect = useCallback((job: Job) => {
    setSelectedJob(job);
  }, []);

  const handleJobAction = useCallback(async (
    action: 'pause' | 'resume' | 'stop' | 'retry' | 'delete',
    jobId: string
  ) => {
    try {
      const response = await fetch(`/api/automation/jobs/${jobId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789',
        },
        body: JSON.stringify({ action }),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} job`);
      }

      // Refresh data after action
      refreshJobs();
      refreshMetrics();
    } catch (err) {
      console.error(`Error ${action}ing job:`, err);
      setError(`Failed to ${action} job`);
    }
  }, [refreshJobs, refreshMetrics]);

  // Bulk operations
  const handleBulkAction = useCallback(async (
    action: 'pause' | 'resume' | 'stop' | 'delete',
    jobIds: string[]
  ) => {
    try {
      const promises = jobIds.map(jobId =>
        fetch(`/api/automation/jobs/${jobId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789',
          },
          body: JSON.stringify({ action }),
        })
      );

      await Promise.all(promises);
      
      // Refresh data after bulk action
      refreshJobs();
      refreshMetrics();
    } catch (err) {
      console.error(`Error performing bulk ${action}:`, err);
      setError(`Failed to perform bulk ${action}`);
    }
  }, [refreshJobs, refreshMetrics]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-white text-xl font-roboto">
          Loading job monitoring dashboard...
        </div>
      </div>
    );
  }

  // Unauthorized state
  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <Card className="max-w-md mx-auto text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-300 mb-6">
            You need administrator privileges to access the job monitoring dashboard.
          </p>
          <Button 
            variant="primary" 
            onClick={() => window.location.href = '/admin'}
          >
            Return to Admin Panel
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white font-roboto">
      <div className="container mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Job Monitoring Dashboard
            </h1>
            <p className="text-gray-300">
              Real-time monitoring and management of background job processing
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              <div 
                className={`w-3 h-3 rounded-full ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`}
              />
              <span className="text-sm text-gray-300">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>

            {/* Refresh Button */}
            <Button
              variant="secondary"
              size="sm"
              onClick={() => {
                refreshJobs();
                refreshMetrics();
              }}
              disabled={isRefreshing}
            >
              {isRefreshing ? '🔄' : '↻'} Refresh
            </Button>

            {/* Back to Admin */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/admin'}
            >
              ← Back to Admin
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2">
              <span className="text-red-400">⚠️</span>
              <span className="text-red-300">{error}</span>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-300"
              >
                ✕
              </button>
            </div>
          </div>
        )}

        {/* Job Metrics Cards */}
        <JobMetricsCards 
          metrics={metrics}
          isLoading={isRefreshing}
          className="mb-8"
        />

        {/* Main Dashboard */}
        <JobMonitoringDashboard
          jobs={jobs}
          metrics={metrics}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          onJobSelect={handleJobSelect}
          onJobAction={handleJobAction}
          onBulkAction={handleBulkAction}
          isLoading={isRefreshing}
          isConnected={isConnected}
        />

        {/* Job Details Modal */}
        {selectedJob && (
          <JobDetailsModal
            job={selectedJob}
            onClose={() => setSelectedJob(null)}
            onJobAction={handleJobAction}
          />
        )}
      </div>
    </div>
  );
}
