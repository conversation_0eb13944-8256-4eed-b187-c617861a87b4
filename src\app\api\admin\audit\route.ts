/**
 * Admin Audit Trail API Endpoints
 * Provides comprehensive audit log management and querying capabilities
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { adminAuditLogger } from '@/lib/audit/admin-audit-logger';
import { AuditLogQueryOptions } from '@/lib/types/audit';
import { log } from '@/lib/logging/logger';

/**
 * GET /api/admin/audit
 * Get audit logs with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const queryOptions: AuditLogQueryOptions = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: Math.min(parseInt(searchParams.get('limit') || '50'), 100), // Max 100 per page
      action: searchParams.get('action') as any,
      resourceType: searchParams.get('resourceType') as any,
      resourceId: searchParams.get('resourceId') || undefined,
      performedBy: searchParams.get('performedBy') || undefined,
      status: searchParams.get('status') as any,
      severity: searchParams.get('severity') as any,
      category: searchParams.get('category') as any,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      searchTerm: searchParams.get('searchTerm') || undefined,
      tags: searchParams.get('tags')?.split(',').filter(Boolean) || undefined,
      includeDetails: searchParams.get('includeDetails') === 'true',
      sortBy: (searchParams.get('sortBy') as any) || 'performed_at',
      sortOrder: (searchParams.get('sortOrder') as any) || 'desc'
    };

    const auditLogs = await adminAuditLogger.getAuditLogs(queryOptions);

    log.admin('audit_logs_retrieved', `Retrieved ${auditLogs.logs.length} audit logs`, {
      page: queryOptions.page,
      limit: queryOptions.limit,
      total: auditLogs.pagination.total
    });

    return NextResponse.json({
      success: true,
      data: auditLogs
    });

  } catch (error) {
    log.error('Error fetching audit logs', error as Error, {
      component: 'audit-api',
      operation: 'get_audit_logs'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch audit logs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/audit
 * Create a new audit log entry (for manual logging)
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const auditRequest = await request.json();

    // Validate required fields
    if (!auditRequest.action || !auditRequest.resourceType || !auditRequest.performedBy) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: action, resourceType, performedBy' 
        },
        { status: 400 }
      );
    }

    // Add request metadata
    auditRequest.ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                            request.headers.get('x-real-ip') ||
                            'unknown';
    auditRequest.userAgent = request.headers.get('user-agent');
    auditRequest.httpMethod = 'POST';
    auditRequest.endpoint = '/api/admin/audit';

    const auditId = await adminAuditLogger.logAction(auditRequest);

    log.admin('manual_audit_logged', `Manual audit log created: ${auditRequest.action}`, {
      auditId,
      action: auditRequest.action,
      resourceType: auditRequest.resourceType,
      performedBy: auditRequest.performedBy
    });

    return NextResponse.json({
      success: true,
      data: { auditId }
    });

  } catch (error) {
    log.error('Error creating audit log', error as Error, {
      component: 'audit-api',
      operation: 'create_audit_log'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to create audit log' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/audit
 * Clean up expired audit logs
 */
export async function DELETE(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const cleanedCount = await adminAuditLogger.cleanupExpiredLogs();

    log.admin('audit_cleanup_performed', `Cleaned up ${cleanedCount} expired audit logs`, {
      cleanedCount
    });

    return NextResponse.json({
      success: true,
      data: {
        cleanedLogs: cleanedCount,
        message: `Cleaned up ${cleanedCount} expired audit log${cleanedCount !== 1 ? 's' : ''}`
      }
    });

  } catch (error) {
    log.error('Error cleaning up audit logs', error as Error, {
      component: 'audit-api',
      operation: 'cleanup_audit_logs'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to cleanup audit logs' },
      { status: 500 }
    );
  }
}
