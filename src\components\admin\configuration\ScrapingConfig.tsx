'use client';

import { useState, useEffect } from 'react';

interface ScrapingConfigProps {
  onSave: () => void;
}

interface ScrapingConfigData {
  scrapeDoConfig: {
    timeout: number;
    retryAttempts: number;
    costOptimization: {
      enabled: boolean;
      targetSavingsPercentage: number;
      creditThreshold: number;
      maxCreditsPerTool: number;
    };
    multiPageScraping: {
      enabled: boolean;
      maxPages: number;
      pageTypes: string[];
    };
  };
  mediaExtraction: {
    ogImageEnabled: boolean;
    faviconEnabled: boolean;
    screenshotFallback: boolean;
    imageQuality: number;
    maxImageSize: number;
  };
}

export function ScrapingConfig({ onSave }: ScrapingConfigProps) {
  const [config, setConfig] = useState<ScrapingConfigData>({
    scrapeDoConfig: {
      timeout: 30000,
      retryAttempts: 3,
      costOptimization: {
        enabled: true,
        targetSavingsPercentage: 60,
        creditThreshold: 100,
        maxCreditsPerTool: 50
      },
      multiPageScraping: {
        enabled: false,
        maxPages: 5,
        pageTypes: ['pricing', 'faq', 'features']
      }
    },
    mediaExtraction: {
      ogImageEnabled: true,
      faviconEnabled: true,
      screenshotFallback: true,
      imageQuality: 80,
      maxImageSize: 2048
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/config?section=scraping', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load scraping configuration');
      }

      const data = await response.json();
      setConfig(data.data);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/admin/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          section: 'scraping',
          action: 'update',
          data: config
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save scraping configuration');
      }

      const result = await response.json();
      
      if (result.validation && !result.validation.isValid) {
        throw new Error(`Validation failed: ${result.validation.errors.map((e: { message: string }) => e.message).join(', ')}`);
      }

      onSave();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setSaving(false);
    }
  };

  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const newConfig = { ...prev };
      const keys = path.split('.');
      let current: any = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  const updatePageTypes = (pageTypes: string[]) => {
    updateConfig('scrapeDoConfig.multiPageScraping.pageTypes', pageTypes);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-400">Loading scraping configuration...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Scraping Configuration</h3>
        <p className="text-gray-400">
          Configure scrape.do API settings, cost optimization, and media extraction options.
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-red-400">
            <span>⚠️</span>
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Scrape.do Configuration */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <h4 className="font-semibold mb-4">Scrape.do API Settings</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Timeout (milliseconds)
            </label>
            <input
              type="number"
              min="5000"
              max="120000"
              step="1000"
              value={config.scrapeDoConfig.timeout}
              onChange={(e) => updateConfig('scrapeDoConfig.timeout', parseInt(e.target.value))}
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
            <p className="text-xs text-gray-400 mt-1">Request timeout (5-120 seconds)</p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Retry Attempts
            </label>
            <input
              type="number"
              min="0"
              max="10"
              value={config.scrapeDoConfig.retryAttempts}
              onChange={(e) => updateConfig('scrapeDoConfig.retryAttempts', parseInt(e.target.value))}
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
            <p className="text-xs text-gray-400 mt-1">Number of retry attempts (0-10)</p>
          </div>
        </div>
      </div>

      {/* Cost Optimization */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold">Cost Optimization</h4>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.scrapeDoConfig.costOptimization.enabled}
              onChange={(e) => updateConfig('scrapeDoConfig.costOptimization.enabled', e.target.checked)}
              className="rounded bg-zinc-600 border-zinc-500"
            />
            <span className="text-sm">Enable cost optimization</span>
          </label>
        </div>

        {config.scrapeDoConfig.costOptimization.enabled && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Target Savings Percentage
              </label>
              <input
                type="number"
                min="0"
                max="90"
                value={config.scrapeDoConfig.costOptimization.targetSavingsPercentage}
                onChange={(e) => updateConfig('scrapeDoConfig.costOptimization.targetSavingsPercentage', parseInt(e.target.value))}
                className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
              />
              <p className="text-xs text-gray-400 mt-1">Target cost reduction (0-90%)</p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Credit Threshold
              </label>
              <input
                type="number"
                min="10"
                max="1000"
                value={config.scrapeDoConfig.costOptimization.creditThreshold}
                onChange={(e) => updateConfig('scrapeDoConfig.costOptimization.creditThreshold', parseInt(e.target.value))}
                className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
              />
              <p className="text-xs text-gray-400 mt-1">Minimum credits for multi-page</p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Max Credits Per Tool
              </label>
              <input
                type="number"
                min="1"
                max="200"
                value={config.scrapeDoConfig.costOptimization.maxCreditsPerTool}
                onChange={(e) => updateConfig('scrapeDoConfig.costOptimization.maxCreditsPerTool', parseInt(e.target.value))}
                className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
              />
              <p className="text-xs text-gray-400 mt-1">Maximum credits per tool</p>
            </div>
          </div>
        )}
      </div>

      {/* Multi-Page Scraping */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold">Multi-Page Scraping</h4>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.scrapeDoConfig.multiPageScraping.enabled}
              onChange={(e) => updateConfig('scrapeDoConfig.multiPageScraping.enabled', e.target.checked)}
              className="rounded bg-zinc-600 border-zinc-500"
            />
            <span className="text-sm">Enable multi-page scraping</span>
          </label>
        </div>

        {config.scrapeDoConfig.multiPageScraping.enabled && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Max Pages Per Tool
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={config.scrapeDoConfig.multiPageScraping.maxPages}
                  onChange={(e) => updateConfig('scrapeDoConfig.multiPageScraping.maxPages', parseInt(e.target.value))}
                  className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
                />
                <p className="text-xs text-gray-400 mt-1">Maximum pages to scrape per tool</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Page Types to Scrape
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {['pricing', 'faq', 'features', 'about'].map((pageType) => (
                  <label key={pageType} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.scrapeDoConfig.multiPageScraping.pageTypes.includes(pageType)}
                      onChange={(e) => {
                        const currentTypes = config.scrapeDoConfig.multiPageScraping.pageTypes;
                        if (e.target.checked) {
                          updatePageTypes([...currentTypes, pageType]);
                        } else {
                          updatePageTypes(currentTypes.filter(t => t !== pageType));
                        }
                      }}
                      className="rounded bg-zinc-600 border-zinc-500"
                    />
                    <span className="text-sm capitalize">{pageType}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Media Extraction */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <h4 className="font-semibold mb-4">Media Extraction</h4>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.mediaExtraction.ogImageEnabled}
                onChange={(e) => updateConfig('mediaExtraction.ogImageEnabled', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500"
              />
              <span className="text-sm">Extract OG Images</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.mediaExtraction.faviconEnabled}
                onChange={(e) => updateConfig('mediaExtraction.faviconEnabled', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500"
              />
              <span className="text-sm">Extract Favicons</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.mediaExtraction.screenshotFallback}
                onChange={(e) => updateConfig('mediaExtraction.screenshotFallback', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500"
              />
              <span className="text-sm">Screenshot Fallback</span>
            </label>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Image Quality (%)
              </label>
              <input
                type="number"
                min="10"
                max="100"
                value={config.mediaExtraction.imageQuality}
                onChange={(e) => updateConfig('mediaExtraction.imageQuality', parseInt(e.target.value))}
                className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
              />
              <p className="text-xs text-gray-400 mt-1">Image compression quality (10-100%)</p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Max Image Size (KB)
              </label>
              <input
                type="number"
                min="100"
                max="10240"
                step="100"
                value={config.mediaExtraction.maxImageSize}
                onChange={(e) => updateConfig('mediaExtraction.maxImageSize', parseInt(e.target.value))}
                className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
              />
              <p className="text-xs text-gray-400 mt-1">Maximum image file size</p>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={saving}
          className="bg-orange-500 hover:bg-orange-600 disabled:bg-orange-700 px-6 py-2 rounded-lg font-medium transition-colors"
        >
          {saving ? 'Saving...' : 'Save Scraping Configuration'}
        </button>
      </div>
    </div>
  );
}
