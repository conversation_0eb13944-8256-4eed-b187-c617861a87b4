'use client';

import { useState } from 'react';
import { AIProviderConfig } from './AIProviderConfig';
import { ScrapingConfig } from './ScrapingConfig';
import { SystemConfig } from './SystemConfig';
import { SecurityConfig } from './SecurityConfig';

interface ConfigurationPanelProps {
  onConfigurationChange?: () => void;
}

type TabType = 'ai-providers' | 'scraping' | 'system' | 'security' | 'import-export';

export function ConfigurationPanel({ onConfigurationChange }: ConfigurationPanelProps) {
  const [activeTab, setActiveTab] = useState<TabType>('ai-providers');
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const tabs = [
    { id: 'ai-providers' as TabType, label: 'AI Providers', icon: '🤖' },
    { id: 'scraping' as TabType, label: 'Scraping', icon: '🕷️' },
    { id: 'system' as TabType, label: 'System Settings', icon: '⚙️' },
    { id: 'security' as TabType, label: 'Security', icon: '🔒' },
    { id: 'import-export' as TabType, label: 'Import/Export', icon: '📁' }
  ];

  const handleConfigurationSave = async () => {
    setSaving(true);
    setMessage(null);

    try {
      // Trigger configuration change callback
      if (onConfigurationChange) {
        await onConfigurationChange();
      }

      setMessage({ type: 'success', text: 'Configuration saved successfully!' });
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to save configuration' 
      });
    } finally {
      setSaving(false);
    }
  };

  const handleTestProviders = async () => {
    setSaving(true);
    setMessage(null);

    try {
      const response = await fetch('/api/admin/config/test-providers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({ detailed: true })
      });

      if (!response.ok) {
        throw new Error('Provider testing failed');
      }

      const data = await response.json();
      
      if (data.overallStatus.overallSuccess) {
        setMessage({ type: 'success', text: 'All providers are working correctly!' });
      } else {
        const failedProviders = Object.entries(data.results)
          .filter(([, result]) => !(result as { success: boolean }).success)
          .map(([provider]) => provider);
        
        setMessage({ 
          type: 'error', 
          text: `Provider test failed for: ${failedProviders.join(', ')}` 
        });
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Provider testing failed' 
      });
    } finally {
      setSaving(false);
    }
  };

  const handleExportConfiguration = async () => {
    try {
      const response = await fetch('/api/admin/config?section=export&includeSecrets=false', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `config-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setMessage({ type: 'success', text: 'Configuration exported successfully!' });
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Export failed' 
      });
    }
  };

  const handleImportConfiguration = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      
      const response = await fetch('/api/admin/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          action: 'import',
          data: { configJson: text }
        })
      });

      if (!response.ok) {
        throw new Error('Import failed');
      }

      const data = await response.json();
      
      if (data.validation && !data.validation.isValid) {
        throw new Error(`Import validation failed: ${data.validation.errors.map((e: { message: string }) => e.message).join(', ')}`);
      }

      setMessage({ type: 'success', text: 'Configuration imported successfully!' });
      
      if (onConfigurationChange) {
        await onConfigurationChange();
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Import failed' 
      });
    }

    // Reset file input
    event.target.value = '';
  };

  return (
    <div className="bg-zinc-800 rounded-lg border border-zinc-700 overflow-hidden">
      {/* Tab Navigation */}
      <div className="border-b border-zinc-700">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-orange-500 text-orange-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 border-b border-zinc-700 ${
          message.type === 'success' ? 'bg-green-900/20 text-green-400' : 'bg-red-900/20 text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            <span>{message.type === 'success' ? '✓' : '✗'}</span>
            <span>{message.text}</span>
          </div>
        </div>
      )}

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'ai-providers' && (
          <AIProviderConfig onSave={handleConfigurationSave} />
        )}

        {activeTab === 'scraping' && (
          <ScrapingConfig onSave={handleConfigurationSave} />
        )}

        {activeTab === 'system' && (
          <SystemConfig onSave={handleConfigurationSave} />
        )}

        {activeTab === 'security' && (
          <SecurityConfig onSave={handleConfigurationSave} />
        )}

        {activeTab === 'import-export' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Import/Export Configuration</h3>
              <p className="text-gray-400 mb-6">
                Export your current configuration for backup or import a previously saved configuration.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Export Section */}
              <div className="bg-zinc-700 rounded-lg p-6">
                <h4 className="font-semibold mb-3">Export Configuration</h4>
                <p className="text-gray-400 text-sm mb-4">
                  Download your current configuration as a JSON file. Sensitive data will be excluded.
                </p>
                <button
                  onClick={handleExportConfiguration}
                  disabled={saving}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  {saving ? 'Exporting...' : 'Export Configuration'}
                </button>
              </div>

              {/* Import Section */}
              <div className="bg-zinc-700 rounded-lg p-6">
                <h4 className="font-semibold mb-3">Import Configuration</h4>
                <p className="text-gray-400 text-sm mb-4">
                  Upload a previously exported configuration file to restore settings.
                </p>
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportConfiguration}
                  className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white file:mr-4 file:py-1 file:px-2 file:rounded file:border-0 file:bg-orange-500 file:text-white hover:file:bg-orange-600"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="border-t border-zinc-700 px-6 py-4 bg-zinc-750">
        <div className="flex justify-between items-center">
          <button
            onClick={handleTestProviders}
            disabled={saving}
            className="bg-green-600 hover:bg-green-700 disabled:bg-green-800 px-4 py-2 rounded-lg font-medium transition-colors"
          >
            {saving ? 'Testing...' : 'Test Providers'}
          </button>

          <div className="flex space-x-3">
            <button
              onClick={() => setMessage(null)}
              className="bg-zinc-600 hover:bg-zinc-500 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Clear Messages
            </button>
            <button
              onClick={handleConfigurationSave}
              disabled={saving}
              className="bg-orange-500 hover:bg-orange-600 disabled:bg-orange-700 px-6 py-2 rounded-lg font-medium transition-colors"
            >
              {saving ? 'Saving...' : 'Save Configuration'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
