#!/usr/bin/env tsx

/**
 * Audit Trail Functionality Test
 * Tests the audit trail system functionality
 */

import { adminAuditLogger } from '@/lib/audit/admin-audit-logger';
import { AuditLogRequest, AuditContext } from '@/lib/types/audit';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

async function testAuditTrailFunctionality() {
  console.log('🧪 Testing Audit Trail Functionality...');
  console.log('=' .repeat(60));

  try {
    // Test 1: Basic audit logging
    console.log('📝 Test 1: Basic audit logging...');
    
    const testAuditRequest: AuditLogRequest = {
      action: 'create_tool',
      resourceType: 'tool',
      resourceId: 'test-tool-123',
      resourceName: 'Test Tool for Audit',
      performedBy: 'test-admin',
      userRole: 'admin',
      sessionId: 'test-session-123',
      requestId: 'test-request-123',
      httpMethod: 'POST',
      endpoint: '/api/admin/tools',
      ipAddress: '127.0.0.1',
      userAgent: 'Test Script',
      actionDetails: {
        test: true,
        timestamp: new Date().toISOString(),
        description: 'Testing audit trail functionality'
      },
      status: 'success',
      severity: 'medium',
      category: 'admin',
      isSensitive: false
    };

    const auditId = await adminAuditLogger.logAction(testAuditRequest);
    console.log(`✅ Audit log created with ID: ${auditId}`);

    // Test 2: Retrieve audit logs
    console.log('📋 Test 2: Retrieving audit logs...');
    
    const auditLogs = await adminAuditLogger.getAuditLogs({
      page: 1,
      limit: 10,
      performedBy: 'test-admin'
    });

    console.log(`✅ Retrieved ${auditLogs.logs.length} audit logs`);
    console.log(`   Total logs: ${auditLogs.pagination.total}`);

    // Test 3: Search audit logs
    console.log('🔍 Test 3: Searching audit logs...');
    
    const searchResults = await adminAuditLogger.searchAuditLogs('Test Tool', {
      resourceType: 'tool',
      limit: 5
    });

    console.log(`✅ Found ${searchResults.length} matching audit logs`);

    // Test 4: Get audit statistics
    console.log('📊 Test 4: Getting audit statistics...');
    
    const statistics = await adminAuditLogger.getAuditStatistics('24h');
    
    console.log(`✅ Audit statistics retrieved:`);
    console.log(`   Total actions: ${statistics.totalActions}`);
    console.log(`   Successful actions: ${statistics.successfulActions}`);
    console.log(`   Failed actions: ${statistics.failedActions}`);
    console.log(`   Error rate: ${statistics.errorRate}%`);

    // Test 5: Convenience methods
    console.log('🎯 Test 5: Testing convenience methods...');
    
    const testContext: AuditContext = {
      userId: 'test-admin',
      userRole: 'admin',
      sessionId: 'test-session-456',
      requestId: 'test-request-456',
      ipAddress: '127.0.0.1',
      userAgent: 'Test Script'
    };

    // Test success logging
    const successAuditId = await adminAuditLogger.logSuccess(
      'update_tool',
      'tool',
      testContext,
      {
        resourceId: 'test-tool-456',
        resourceName: 'Updated Test Tool',
        actionDetails: { updated: true }
      }
    );
    console.log(`✅ Success audit logged with ID: ${successAuditId}`);

    // Test failure logging
    const failureAuditId = await adminAuditLogger.logFailure(
      'delete_tool',
      'tool',
      testContext,
      'Tool not found for deletion',
      {
        resourceId: 'test-tool-404',
        severity: 'high'
      }
    );
    console.log(`✅ Failure audit logged with ID: ${failureAuditId}`);

    // Test 6: Verify data integrity
    console.log('🔒 Test 6: Verifying data integrity...');
    
    const verificationLogs = await adminAuditLogger.getAuditLogs({
      page: 1,
      limit: 5,
      performedBy: 'test-admin',
      sortBy: 'performed_at',
      sortOrder: 'desc'
    });

    if (verificationLogs.logs.length >= 3) {
      const latestLogs = verificationLogs.logs.slice(0, 3);
      console.log(`✅ Data integrity verified:`);
      console.log(`   Latest log action: ${latestLogs[0].action}`);
      console.log(`   Latest log status: ${latestLogs[0].status}`);
      console.log(`   All logs have timestamps: ${latestLogs.every(log => log.performedAt)}`);
      console.log(`   All logs have user info: ${latestLogs.every(log => log.performedBy)}`);
    }

    // Cleanup test data
    console.log('🧹 Cleaning up test data...');
    
    // Note: In a real implementation, you might want to clean up test data
    // For now, we'll leave the test data as it demonstrates the audit trail
    console.log('✅ Test data preserved for audit trail demonstration');

    console.log('');
    console.log('🎉 All Audit Trail Tests Passed!');
    console.log('');
    console.log('📋 Test Summary:');
    console.log('   ✅ Basic audit logging');
    console.log('   ✅ Audit log retrieval with pagination');
    console.log('   ✅ Audit log search functionality');
    console.log('   ✅ Audit statistics generation');
    console.log('   ✅ Convenience methods (logSuccess/logFailure)');
    console.log('   ✅ Data integrity verification');
    console.log('');
    console.log('🔧 Audit Trail System Status: FULLY OPERATIONAL');

  } catch (error) {
    console.error('❌ Audit trail test failed:', error);
    
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      if (error.stack) {
        console.error('Stack trace:', error.stack);
      }
    }
    
    process.exit(1);
  }
}

// Run the test
testAuditTrailFunctionality();
