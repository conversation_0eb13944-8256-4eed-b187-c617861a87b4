/**
 * Audit Middleware Tests
 * Test suite for automatic audit logging middleware
 */

import { NextRequest, NextResponse } from 'next/server';
import { AuditMiddleware } from '@/lib/audit/audit-middleware';
import { adminAuditLogger } from '@/lib/audit/admin-audit-logger';

// Mock dependencies
jest.mock('@/lib/audit/admin-audit-logger', () => ({
  adminAuditLogger: {
    logSuccess: jest.fn(),
    logFailure: jest.fn()
  }
}));

jest.mock('@/lib/logging/logger', () => ({
  log: {
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('AuditMiddleware', () => {
  let auditMiddleware: AuditMiddleware;
  let mockAdminAuditLogger: jest.Mocked<typeof adminAuditLogger>;

  beforeEach(() => {
    auditMiddleware = new AuditMiddleware();
    mockAdminAuditLogger = adminAuditLogger as jest.Mocked<typeof adminAuditLogger>;
    jest.clearAllMocks();
  });

  describe('withAudit', () => {
    it('should wrap handler and log successful operations', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ success: true }, { status: 200 })
      );

      const wrappedHandler = auditMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/tools', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
          'x-user-id': 'admin-user',
          'x-user-role': 'admin',
          'x-session-id': 'session-123',
          'user-agent': 'Mozilla/5.0'
        },
        body: JSON.stringify({
          name: 'Test Tool',
          slug: 'test-tool'
        })
      });

      mockAdminAuditLogger.logSuccess.mockResolvedValue('audit-id-123');

      const response = await wrappedHandler(mockRequest);

      expect(mockHandler).toHaveBeenCalledWith(mockRequest);
      expect(response.status).toBe(200);
      expect(mockAdminAuditLogger.logSuccess).toHaveBeenCalledWith(
        'create_tool',
        'tool',
        expect.objectContaining({
          userId: 'admin-user',
          userRole: 'admin',
          sessionId: 'session-123'
        }),
        expect.objectContaining({
          actionDetails: expect.objectContaining({
            endpoint: '/api/admin/tools',
            httpMethod: 'POST',
            responseStatus: 200,
            requestData: {
              name: 'Test Tool',
              slug: 'test-tool'
            }
          })
        })
      );
    });

    it('should log failed operations when handler throws error', async () => {
      const mockError = new Error('Database connection failed');
      const mockHandler = jest.fn().mockRejectedValue(mockError);

      const wrappedHandler = auditMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/tools/123', {
        method: 'DELETE',
        headers: {
          'x-user-id': 'admin-user',
          'x-user-role': 'admin',
          'x-session-id': 'session-123'
        }
      });

      mockAdminAuditLogger.logFailure.mockResolvedValue('audit-failure-id');

      await expect(wrappedHandler(mockRequest)).rejects.toThrow('Database connection failed');

      expect(mockAdminAuditLogger.logFailure).toHaveBeenCalledWith(
        'delete_tool',
        'tool',
        expect.objectContaining({
          userId: 'admin-user',
          userRole: 'admin',
          sessionId: 'session-123'
        }),
        mockError,
        expect.objectContaining({
          endpoint: '/api/admin/tools/123',
          httpMethod: 'DELETE'
        })
      );
    });

    it('should skip audit logging for non-auditable endpoints', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ success: true })
      );

      const wrappedHandler = auditMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/public/tools', {
        method: 'GET'
      });

      await wrappedHandler(mockRequest);

      expect(mockHandler).toHaveBeenCalledWith(mockRequest);
      expect(mockAdminAuditLogger.logSuccess).not.toHaveBeenCalled();
      expect(mockAdminAuditLogger.logFailure).not.toHaveBeenCalled();
    });

    it('should handle missing audit context gracefully', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ success: true })
      );

      const wrappedHandler = auditMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/tools', {
        method: 'POST',
        headers: {
          'content-type': 'application/json'
        },
        body: JSON.stringify({ name: 'Test Tool' })
      });

      mockAdminAuditLogger.logSuccess.mockResolvedValue('audit-id-123');

      await wrappedHandler(mockRequest);

      expect(mockAdminAuditLogger.logSuccess).toHaveBeenCalledWith(
        'create_tool',
        'tool',
        expect.objectContaining({
          userId: 'system', // Default when no user ID provided
          userRole: 'admin'
        }),
        expect.any(Object)
      );
    });

    it('should extract resource information from request body', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ success: true })
      );

      const wrappedHandler = auditMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/categories', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
          'x-user-id': 'admin-user'
        },
        body: JSON.stringify({
          id: 'cat-123',
          title: 'Test Category',
          description: 'A test category'
        })
      });

      mockAdminAuditLogger.logSuccess.mockResolvedValue('audit-id-123');

      await wrappedHandler(mockRequest);

      expect(mockAdminAuditLogger.logSuccess).toHaveBeenCalledWith(
        'create_category',
        'category',
        expect.any(Object),
        expect.objectContaining({
          resourceId: 'cat-123',
          resourceName: 'Test Category'
        })
      );
    });

    it('should sanitize sensitive data from request body', async () => {
      const auditMiddlewareWithSensitiveFields = new AuditMiddleware({
        sensitiveFields: ['password', 'api_key', 'secret']
      });

      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ success: true })
      );

      const wrappedHandler = auditMiddlewareWithSensitiveFields.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/config', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
          'x-user-id': 'admin-user'
        },
        body: JSON.stringify({
          api_key: 'secret-key-123',
          password: 'super-secret',
          public_setting: 'visible-value'
        })
      });

      mockAdminAuditLogger.logSuccess.mockResolvedValue('audit-id-123');

      await wrappedHandler(mockRequest);

      expect(mockAdminAuditLogger.logSuccess).toHaveBeenCalledWith(
        'update_config',
        'config',
        expect.any(Object),
        expect.objectContaining({
          actionDetails: expect.objectContaining({
            requestData: {
              api_key: '[REDACTED]',
              password: '[REDACTED]',
              public_setting: 'visible-value'
            }
          })
        })
      );
    });

    it('should determine error severity correctly', async () => {
      const mockHandler = jest.fn().mockRejectedValue(new Error('Unauthorized access'));

      const wrappedHandler = auditMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/config', {
        method: 'PUT',
        headers: {
          'x-user-id': 'admin-user'
        }
      });

      mockAdminAuditLogger.logFailure.mockResolvedValue('audit-failure-id');

      await expect(wrappedHandler(mockRequest)).rejects.toThrow('Unauthorized access');

      expect(mockAdminAuditLogger.logFailure).toHaveBeenCalledWith(
        'update_config',
        'config',
        expect.any(Object),
        expect.any(Error),
        expect.objectContaining({
          severity: 'critical' // Unauthorized errors should be critical
        })
      );
    });

    it('should handle audit logging failures gracefully', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ success: true })
      );

      const wrappedHandler = auditMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/tools', {
        method: 'POST',
        headers: {
          'x-user-id': 'admin-user'
        },
        body: JSON.stringify({ name: 'Test Tool' })
      });

      // Mock audit logging failure
      mockAdminAuditLogger.logSuccess.mockRejectedValue(new Error('Audit system down'));

      // Handler should still complete successfully even if audit logging fails
      const response = await wrappedHandler(mockRequest);

      expect(response.status).toBe(200);
      expect(mockHandler).toHaveBeenCalled();
    });

    it('should extract client IP address correctly', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ success: true })
      );

      const wrappedHandler = auditMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/tools', {
        method: 'POST',
        headers: {
          'x-user-id': 'admin-user',
          'x-forwarded-for': '*************, ********',
          'x-real-ip': '*************'
        },
        body: JSON.stringify({ name: 'Test Tool' })
      });

      mockAdminAuditLogger.logSuccess.mockResolvedValue('audit-id-123');

      await wrappedHandler(mockRequest);

      expect(mockAdminAuditLogger.logSuccess).toHaveBeenCalledWith(
        'create_tool',
        'tool',
        expect.objectContaining({
          ipAddress: '*************' // Should extract first IP from x-forwarded-for
        }),
        expect.any(Object)
      );
    });

    it('should work with disabled audit middleware', async () => {
      const disabledMiddleware = new AuditMiddleware({ enabled: false });

      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ success: true })
      );

      const wrappedHandler = disabledMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/tools', {
        method: 'POST'
      });

      await wrappedHandler(mockRequest);

      expect(mockHandler).toHaveBeenCalledWith(mockRequest);
      expect(mockAdminAuditLogger.logSuccess).not.toHaveBeenCalled();
    });

    it('should exclude specified endpoints from auditing', async () => {
      const selectiveMiddleware = new AuditMiddleware({
        excludeEndpoints: ['/api/admin/health']
      });

      const mockHandler = jest.fn().mockResolvedValue(
        NextResponse.json({ status: 'healthy' })
      );

      const wrappedHandler = selectiveMiddleware.withAudit(mockHandler);

      const mockRequest = new NextRequest('http://localhost:3000/api/admin/health', {
        method: 'GET'
      });

      await wrappedHandler(mockRequest);

      expect(mockHandler).toHaveBeenCalledWith(mockRequest);
      expect(mockAdminAuditLogger.logSuccess).not.toHaveBeenCalled();
    });
  });
});
