#!/usr/bin/env tsx

/**
 * Audit Trail Migration Script
 * Applies the admin audit trail database schema migration
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runAuditTrailMigration() {
  console.log('🚀 Starting Audit Trail Migration...');
  console.log('=' .repeat(60));

  try {
    // Read the migration SQL file
    const migrationPath = join(process.cwd(), 'src/lib/database/migrations/004_admin_audit_trail_schema.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');

    console.log('📄 Loaded migration file: 004_admin_audit_trail_schema.sql');

    // Execute the migration
    console.log('⚡ Executing audit trail schema migration...');
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

    if (error) {
      // Try direct execution if RPC fails
      console.log('⚠️  RPC execution failed, trying direct execution...');
      
      // Split SQL into individual statements and execute them
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          const { error: execError } = await supabase.rpc('exec_sql', { sql: statement });
          if (execError) {
            console.error(`❌ Error executing statement: ${execError.message}`);
            console.error(`Statement: ${statement.substring(0, 100)}...`);
          }
        }
      }
    }

    // Verify tables were created
    console.log('🔍 Verifying audit trail tables...');
    
    const expectedTables = [
      'admin_audit_log',
      'admin_user_sessions', 
      'audit_log_statistics'
    ];

    for (const tableName of expectedTables) {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (error) {
        console.error(`❌ Table ${tableName} verification failed: ${error.message}`);
      } else {
        console.log(`✅ Table ${tableName} exists and is accessible`);
      }
    }

    // Verify functions were created
    console.log('🔍 Verifying audit trail functions...');
    
    const { data: functions, error: funcError } = await supabase
      .rpc('exec_sql', { 
        sql: `
          SELECT routine_name 
          FROM information_schema.routines 
          WHERE routine_schema = 'public' 
          AND routine_name IN (
            'update_session_activity',
            'cleanup_expired_audit_logs',
            'detect_suspicious_activity'
          );
        `
      });

    if (funcError) {
      console.warn('⚠️  Could not verify functions:', funcError.message);
    } else {
      console.log('✅ Audit trail functions verified');
    }

    // Test basic audit logging functionality
    console.log('🧪 Testing basic audit logging...');
    
    const testAuditEntry = {
      action: 'test_migration',
      resource_type: 'system',
      resource_id: 'migration-test',
      resource_name: 'Audit Trail Migration Test',
      performed_by: 'system',
      user_role: 'admin',
      session_id: 'migration-session',
      request_id: 'migration-request',
      http_method: 'POST',
      endpoint: '/api/admin/audit/test',
      ip_address: '127.0.0.1',
      user_agent: 'Migration Script',
      action_details: {
        test: true,
        migration: 'audit_trail',
        timestamp: new Date().toISOString()
      },
      status: 'success',
      severity: 'low',
      category: 'system',
      is_sensitive: false
    };

    const { data: auditResult, error: auditError } = await supabase
      .from('admin_audit_log')
      .insert([testAuditEntry])
      .select()
      .single();

    if (auditError) {
      console.error('❌ Audit logging test failed:', auditError.message);
    } else {
      console.log('✅ Audit logging test successful');
      console.log(`   Audit ID: ${auditResult.id}`);
      
      // Clean up test entry
      await supabase
        .from('admin_audit_log')
        .delete()
        .eq('id', auditResult.id);
      
      console.log('🧹 Test audit entry cleaned up');
    }

    console.log('');
    console.log('🎉 Audit Trail Migration Completed Successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('   ✅ Database schema created');
    console.log('   ✅ Tables: admin_audit_log, admin_user_sessions, audit_log_statistics');
    console.log('   ✅ Indexes and constraints applied');
    console.log('   ✅ Functions and triggers created');
    console.log('   ✅ Basic functionality tested');
    console.log('');
    console.log('🔧 Next Steps:');
    console.log('   1. Update admin API routes to use audit middleware');
    console.log('   2. Configure audit retention policies');
    console.log('   3. Set up audit log monitoring and alerts');
    console.log('   4. Test audit trail functionality in admin panel');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runAuditTrailMigration();
