#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function testAIConfigPersistence() {
  console.log('🧪 Testing AI Configuration Persistence');
  console.log('============================================================');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Test 1: Check if configuration column exists
    console.log('📋 Test 1: Checking if configuration column exists...');
    
    const { data: configTest, error: configTestError } = await supabase
      .from('system_configuration')
      .select('config_key, configuration')
      .eq('config_key', 'default_environment_config')
      .single();

    if (configTestError) {
      console.log('❌ Configuration column test failed:', configTestError.message);
      if (configTestError.message.includes('column "configuration" does not exist')) {
        console.log('🔧 Please run the manual-migration-002.sql file first!');
        return;
      }
    } else {
      console.log('✅ Configuration column exists and is accessible');
      if (configTest?.configuration) {
        console.log('📊 Configuration data found:', Object.keys(configTest.configuration).join(', '));
      }
    }

    // Test 2: Test the AI config API endpoint
    console.log('\n📋 Test 2: Testing AI config API endpoint...');
    
    const response = await fetch('http://localhost:3001/api/admin/config?section=ai-providers', {
      headers: {
        'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
      }
    });

    if (!response.ok) {
      console.log('❌ API endpoint test failed:', response.status, response.statusText);
    } else {
      const data = await response.json();
      console.log('✅ API endpoint accessible');
      console.log('📊 API response structure:', Object.keys(data));
      
      if (data.data) {
        console.log('📊 AI provider config keys:', Object.keys(data.data));
        
        // Check if all expected fields are present
        const expectedFields = ['openai', 'openrouter', 'modelSelection'];
        const missingFields = expectedFields.filter(field => !data.data[field]);
        
        if (missingFields.length === 0) {
          console.log('✅ All expected AI provider fields are present');
          
          // Check OpenAI configuration
          if (data.data.openai) {
            const openaiFields = Object.keys(data.data.openai);
            console.log('📊 OpenAI config fields:', openaiFields.join(', '));
            
            const expectedOpenAIFields = ['enabled', 'model', 'maxTokens', 'temperature', 'timeout', 'priority'];
            const missingOpenAIFields = expectedOpenAIFields.filter(field => !(field in data.data.openai));
            
            if (missingOpenAIFields.length === 0) {
              console.log('✅ All OpenAI configuration fields are present');
            } else {
              console.log('⚠️  Missing OpenAI fields:', missingOpenAIFields.join(', '));
            }
          }
          
          // Check OpenRouter configuration
          if (data.data.openrouter) {
            const openrouterFields = Object.keys(data.data.openrouter);
            console.log('📊 OpenRouter config fields:', openrouterFields.join(', '));
            
            const expectedOpenRouterFields = ['enabled', 'model', 'maxTokens', 'temperature', 'timeout', 'priority', 'implicitCaching'];
            const missingOpenRouterFields = expectedOpenRouterFields.filter(field => !(field in data.data.openrouter));
            
            if (missingOpenRouterFields.length === 0) {
              console.log('✅ All OpenRouter configuration fields are present');
            } else {
              console.log('⚠️  Missing OpenRouter fields:', missingOpenRouterFields.join(', '));
            }
          }
        } else {
          console.log('⚠️  Missing AI provider fields:', missingFields.join(', '));
        }
      }
    }

    // Test 3: Test saving configuration
    console.log('\n📋 Test 3: Testing configuration save...');
    
    const testConfig = {
      openai: {
        enabled: true,
        model: 'gpt-4o-test-model',
        maxTokens: 8000,
        temperature: 0.8,
        timeout: 60000,
        priority: 1
      },
      openrouter: {
        enabled: true,
        model: 'google/gemini-test-model',
        maxTokens: 16000,
        temperature: 0.6,
        implicitCaching: true,
        timeout: 120000,
        priority: 2
      },
      modelSelection: {
        strategy: 'auto',
        fallbackOrder: ['openai', 'openrouter'],
        costThreshold: 0.02,
        qualityThreshold: 0.9
      }
    };

    const saveResponse = await fetch('http://localhost:3001/api/admin/config', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
      },
      body: JSON.stringify({
        section: 'ai-providers',
        action: 'update',
        data: testConfig
      })
    });

    if (!saveResponse.ok) {
      console.log('❌ Save test failed:', saveResponse.status, saveResponse.statusText);
    } else {
      const saveResult = await saveResponse.json();
      console.log('✅ Configuration save successful');
      console.log('📊 Save result:', saveResult);
    }

    // Test 4: Test loading saved configuration
    console.log('\n📋 Test 4: Testing configuration load after save...');
    
    const loadResponse = await fetch('http://localhost:3001/api/admin/config?section=ai-providers', {
      headers: {
        'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
      }
    });

    if (!loadResponse.ok) {
      console.log('❌ Load test failed:', loadResponse.status, loadResponse.statusText);
    } else {
      const loadData = await loadResponse.json();
      console.log('✅ Configuration load successful');
      
      // Verify the saved values
      if (loadData.data?.openai?.model === testConfig.openai.model) {
        console.log('✅ OpenAI model persisted correctly');
      } else {
        console.log('❌ OpenAI model not persisted correctly');
        console.log(`   Expected: ${testConfig.openai.model}`);
        console.log(`   Got: ${loadData.data?.openai?.model}`);
      }
      
      if (loadData.data?.openai?.maxTokens === testConfig.openai.maxTokens) {
        console.log('✅ OpenAI maxTokens persisted correctly');
      } else {
        console.log('❌ OpenAI maxTokens not persisted correctly');
        console.log(`   Expected: ${testConfig.openai.maxTokens}`);
        console.log(`   Got: ${loadData.data?.openai?.maxTokens}`);
      }
      
      if (loadData.data?.openai?.temperature === testConfig.openai.temperature) {
        console.log('✅ OpenAI temperature persisted correctly');
      } else {
        console.log('❌ OpenAI temperature not persisted correctly');
        console.log(`   Expected: ${testConfig.openai.temperature}`);
        console.log(`   Got: ${loadData.data?.openai?.temperature}`);
      }
    }

    console.log('\n🎉 AI Configuration Persistence Test Complete!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testAIConfigPersistence().catch(console.error);
