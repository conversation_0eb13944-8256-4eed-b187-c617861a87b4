#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function checkDatabaseSchema() {
  console.log('🔍 Checking database schema for system_configuration table...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Check table structure
    console.log('📋 Checking system_configuration table columns...');
    const { data: columns, error: columnsError } = await supabase
      .rpc('get_table_columns', { table_name: 'system_configuration' })
      .select();

    if (columnsError) {
      console.log('⚠️  RPC function not available, trying direct query...');
      
      // Try a direct query to check if the table exists and what columns it has
      const { data: tableData, error: tableError } = await supabase
        .from('system_configuration')
        .select('*')
        .limit(1);

      if (tableError) {
        console.error('❌ Error querying system_configuration table:', tableError);
        return;
      }

      console.log('✅ Table exists and is accessible');
      
      if (tableData && tableData.length > 0) {
        console.log('📊 Sample record structure:');
        console.log(JSON.stringify(tableData[0], null, 2));
        
        console.log('\n🔑 Available columns:');
        Object.keys(tableData[0]).forEach(key => {
          console.log(`  - ${key}`);
        });
      } else {
        console.log('📊 Table is empty, checking with INSERT to see column structure...');
        
        // Try to insert a test record to see what columns are available
        const { error: insertError } = await supabase
          .from('system_configuration')
          .insert({
            config_key: 'test_schema_check',
            config_value: { test: true },
            config_type: 'system',
            description: 'Test record for schema checking'
          });

        if (insertError) {
          console.log('❌ Insert error (this helps us see the schema):', insertError.message);
        } else {
          console.log('✅ Test insert successful');
          
          // Clean up test record
          await supabase
            .from('system_configuration')
            .delete()
            .eq('config_key', 'test_schema_check');
        }
      }
    } else {
      console.log('✅ Table columns:', columns);
    }

    // Check for specific configuration records
    console.log('\n🔍 Checking for existing configuration records...');
    const { data: configs, error: configsError } = await supabase
      .from('system_configuration')
      .select('config_key, config_type, created_at')
      .order('created_at', { ascending: false });

    if (configsError) {
      console.error('❌ Error fetching configurations:', configsError);
    } else {
      console.log(`📊 Found ${configs?.length || 0} configuration records:`);
      configs?.forEach(config => {
        console.log(`  - ${config.config_key} (${config.config_type})`);
      });
    }

    // Try to check if the configuration column exists by attempting to select it
    console.log('\n🔍 Testing configuration column access...');
    const { data: configTest, error: configTestError } = await supabase
      .from('system_configuration')
      .select('config_key, configuration')
      .limit(1);

    if (configTestError) {
      console.log('❌ Configuration column not accessible:', configTestError.message);
      console.log('🔧 This explains why the configuration manager is falling back to key-value pairs');
    } else {
      console.log('✅ Configuration column is accessible');
      if (configTest && configTest.length > 0) {
        console.log('📊 Sample configuration data:', configTest[0]);
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkDatabaseSchema().catch(console.error);
