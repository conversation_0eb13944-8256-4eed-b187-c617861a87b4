/**
 * Unit Tests for Multi-Page Scraper
 * Tests page discovery, URL pattern matching, content extraction, and data storage
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { MultiPageScraper } from '@/lib/scraping/multi-page-scraper';
import { MultiPageScrapingConfig, ScrapingDecision, PageDiscoveryResult } from '@/lib/scraping/types';

// Mock the cost optimizer
jest.mock('@/lib/scraping/cost-optimizer', () => ({
  costOptimizer: {
    scrapeWithMaxCostOptimization: jest.fn().mockResolvedValue({
      success: true,
      content: 'Mocked scraped content',
      metadata: {
        creditsUsed: 5,
        requestType: 'Enhanced'
      }
    }),
    categorizeUrlsByPattern: jest.fn().mockReturnValue({
      neverEnhance: [],
      alwaysEnhance: [],
      intelligent: []
    })
  }
}));

// Mock the scrape-do client
jest.mock('@/lib/scraping/scrape-do-client', () => ({
  scrapeDoClient: {
    getUsageStatistics: jest.fn().mockResolvedValue({
      remainingMonthlyRequests: 1000,
      usedMonthlyRequests: 500,
      isActive: true
    })
  }
}));

describe('MultiPageScraper', () => {
  let scraper: MultiPageScraper;
  let defaultConfig: MultiPageScrapingConfig;

  beforeEach(() => {
    defaultConfig = {
      enabled: true,
      mode: 'conditional',
      maxPagesPerTool: 4,
      creditThreshold: 100,
      pageTypes: {
        pricing: {
          enabled: true,
          priority: 'high',
          patterns: ['/pricing', '/plans', '/subscription'],
          selectors: ['.pricing', '.plans', '[class*="price"]'],
          required: true
        },
        faq: {
          enabled: true,
          priority: 'medium',
          patterns: ['/faq', '/help', '/support'],
          selectors: ['.faq', '.help', '[class*="faq"]'],
          required: false
        },
        features: {
          enabled: true,
          priority: 'high',
          patterns: ['/features', '/capabilities'],
          selectors: ['.features', '[class*="feature"]'],
          required: true
        },
        about: {
          enabled: false,
          priority: 'low',
          patterns: ['/about', '/company'],
          selectors: ['.about', '[class*="about"]'],
          required: false
        }
      },
      fallbackStrategy: {
        searchInMainPage: true,
        useNavigation: true,
        useSitemap: false
      }
    };

    scraper = new MultiPageScraper(defaultConfig);
    jest.clearAllMocks();
  });

  describe('Configuration Management', () => {
    it('should initialize with default configuration', () => {
      const defaultScraper = new MultiPageScraper();
      expect(defaultScraper).toBeInstanceOf(MultiPageScraper);
    });

    it('should accept custom configuration', () => {
      const customConfig: Partial<MultiPageScrapingConfig> = {
        enabled: false,
        maxPagesPerTool: 2
      };
      
      const customScraper = new MultiPageScraper(customConfig);
      expect(customScraper).toBeInstanceOf(MultiPageScraper);
    });

    it('should update configuration dynamically', () => {
      const newConfig: Partial<MultiPageScrapingConfig> = {
        maxPagesPerTool: 6,
        creditThreshold: 200
      };

      scraper.updateConfig(newConfig);
      // Configuration should be updated internally
      expect(scraper).toBeInstanceOf(MultiPageScraper);
    });
  });

  describe('Page Discovery', () => {
    it('should discover pricing pages from content', async () => {
      const mainUrl = 'https://example.com';
      const mainContent = `
        <html>
          <body>
            <nav>
              <a href="/pricing">Pricing</a>
              <a href="/plans">Plans</a>
              <a href="/features">Features</a>
            </nav>
            <div class="pricing">
              <h2>Our Pricing Plans</h2>
              <div class="plan">Basic Plan - $10/month</div>
            </div>
          </body>
        </html>
      `;

      const decision = await scraper.discoverAndPlanScraping(mainUrl, mainContent);

      expect(decision).toBeDefined();
      expect(decision.scrapeNow).toBeDefined();
      expect(decision.reason).toBeDefined();
    });

    it('should handle disabled multi-page scraping', async () => {
      const disabledConfig = { ...defaultConfig, enabled: false };
      const disabledScraper = new MultiPageScraper(disabledConfig);

      const decision = await disabledScraper.discoverAndPlanScraping(
        'https://example.com',
        '<html><body>Test content</body></html>'
      );

      expect(decision.scrapeNow).toHaveLength(0);
      expect(decision.reason).toBe('Multi-page scraping disabled');
    });

    it('should find content in main page when available', async () => {
      const mainUrl = 'https://example.com';
      const mainContent = `
        <html>
          <body>
            <div class="faq">
              <h2>Frequently Asked Questions</h2>
              <div class="question">What is this service?</div>
              <div class="answer">This is a test service.</div>
            </div>
          </body>
        </html>
      `;

      const decision = await scraper.discoverAndPlanScraping(mainUrl, mainContent);

      expect(decision).toBeDefined();
      // Should find FAQ content in main page
      const faqPage = decision.scrapeNow.find(page => page.pageType === 'faq');
      if (faqPage) {
        expect(faqPage.foundMethod).toBe('content');
        expect(faqPage.url).toBe(mainUrl);
      }
    });

    it('should discover pages by URL patterns', async () => {
      const mainUrl = 'https://example.com';
      const mainContent = `
# Welcome to Example.com

## Navigation

- [View Pricing](/pricing)
- [Help Center](/help)
- [Features](/features)

## Main Content

This is the main page content.
      `;

      const decision = await scraper.discoverAndPlanScraping(mainUrl, mainContent);

      expect(decision.scrapeNow.length).toBeGreaterThan(0);

      // Should discover pricing page
      const pricingPage = decision.scrapeNow.find(page => page.pageType === 'pricing');
      expect(pricingPage).toBeDefined();
      if (pricingPage) {
        expect(pricingPage.url).toBe('https://example.com/pricing');
        expect(pricingPage.foundMethod).toBe('link');
      }
    });

    it('should handle pages with no discoverable content', async () => {
      // Create a scraper with strict mode that doesn't use pattern fallback
      const strictConfig = {
        ...defaultConfig,
        mode: 'strict' as const, // Only scrape if explicitly found
        fallbackStrategy: {
          searchInMainPage: true,
          useNavigation: true,
          useSitemap: false
        }
      };
      const strictScraper = new MultiPageScraper(strictConfig);

      const mainUrl = 'https://example.com';
      const mainContent = `
# Welcome

This is a simple page with no special sections.
      `;

      const decision = await strictScraper.discoverAndPlanScraping(mainUrl, mainContent);

      expect(decision.scrapeNow).toHaveLength(0);
      expect(decision.reason).toContain('No pages discovered');
    });
  });

  describe('Credit Management', () => {
    it('should consider credit threshold in decision making', async () => {
      // Mock low credits
      const { scrapeDoClient } = require('@/lib/scraping/scrape-do-client');
      scrapeDoClient.getUsageStatistics.mockResolvedValue({
        remainingMonthlyRequests: 50, // Below threshold
        usedMonthlyRequests: 950,
        isActive: true
      });

      const mainUrl = 'https://example.com';
      const mainContent = `
# Example.com

## Navigation

- [Pricing](/pricing)
- [Features](/features)
      `;

      const decision = await scraper.discoverAndPlanScraping(mainUrl, mainContent);

      // Should queue for later due to low credits
      expect(decision.queueForLater.length).toBeGreaterThan(0);
      expect(decision.reason).toContain('credits');
    });

    it('should proceed with scraping when credits are sufficient', async () => {
      // Mock sufficient credits
      const { scrapeDoClient } = require('@/lib/scraping/scrape-do-client');
      scrapeDoClient.getUsageStatistics.mockResolvedValue({
        remainingMonthlyRequests: 500, // Above threshold
        usedMonthlyRequests: 500,
        isActive: true
      });

      const mainUrl = 'https://example.com';
      const mainContent = `
# Example.com

## Navigation

- [Pricing](/pricing)
      `;

      const decision = await scraper.discoverAndPlanScraping(mainUrl, mainContent);

      expect(decision.scrapeNow.length).toBeGreaterThan(0);
    });
  });

  describe('Page Type Configuration', () => {
    it('should respect enabled/disabled page types', async () => {
      const configWithDisabledAbout = {
        ...defaultConfig,
        pageTypes: {
          ...defaultConfig.pageTypes,
          about: {
            ...defaultConfig.pageTypes.about,
            enabled: false
          }
        }
      };

      const scraperWithConfig = new MultiPageScraper(configWithDisabledAbout);

      const mainUrl = 'https://example.com';
      const mainContent = `
# Example.com

## Navigation

- [About Us](/about)
- [Pricing](/pricing)
      `;

      const decision = await scraperWithConfig.discoverAndPlanScraping(mainUrl, mainContent);

      // Should not include about page
      const aboutPage = decision.scrapeNow.find(page => page.pageType === 'about');
      expect(aboutPage).toBeUndefined();

      // Should include pricing page
      const pricingPage = decision.scrapeNow.find(page => page.pageType === 'pricing');
      expect(pricingPage).toBeDefined();
    });

    it('should prioritize pages correctly', async () => {
      const mainUrl = 'https://example.com';
      const mainContent = `
# Example.com

## Navigation

- [Pricing](/pricing)
- [FAQ](/faq)
- [Features](/features)
      `;

      const decision = await scraper.discoverAndPlanScraping(mainUrl, mainContent);

      // High priority pages (pricing, features) should come before medium priority (faq)
      const sortedPages = decision.scrapeNow.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      expect(sortedPages[0].priority).toBe('high');
    });
  });

  describe('Execution', () => {
    it('should execute multi-page scraping successfully', async () => {
      const decision: ScrapingDecision = {
        scrapeNow: [
          {
            pageType: 'pricing',
            url: 'https://example.com/pricing',
            confidence: 0.9,
            foundMethod: 'link',
            priority: 'high',
            estimatedCredits: 5
          }
        ],
        queueForLater: [],
        skipPages: [],
        reason: 'Sufficient credits available'
      };

      const results = await scraper.executeMultiPageScraping(decision);

      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(true);
      expect(results[0].metadata?.pageType).toBe('pricing');
    });

    it('should handle scraping failures gracefully', async () => {
      const { costOptimizer } = require('@/lib/scraping/cost-optimizer');
      costOptimizer.scrapeWithMaxCostOptimization.mockRejectedValue(new Error('Scraping failed'));

      const decision: ScrapingDecision = {
        scrapeNow: [
          {
            pageType: 'pricing',
            url: 'https://example.com/pricing',
            confidence: 0.9,
            foundMethod: 'link',
            priority: 'high',
            estimatedCredits: 5
          }
        ],
        queueForLater: [],
        skipPages: [],
        reason: 'Test execution'
      };

      const results = await scraper.executeMultiPageScraping(decision);

      // Should handle the error and continue
      expect(results).toHaveLength(0); // No successful results
    });
  });

  describe('Pattern Matching', () => {
    it('should match URL patterns correctly', async () => {
      const testCases = [
        { url: '/pricing', pageType: 'pricing', shouldMatch: true },
        { url: '/plans', pageType: 'pricing', shouldMatch: true },
        { url: '/subscription', pageType: 'pricing', shouldMatch: true },
        { url: '/faq', pageType: 'faq', shouldMatch: true },
        { url: '/help', pageType: 'faq', shouldMatch: true },
        { url: '/features', pageType: 'features', shouldMatch: true },
        { url: '/random', pageType: 'pricing', shouldMatch: false }
      ];

      for (const testCase of testCases) {
        // Create a targeted config that only enables the specific page type being tested
        const targetedConfig = {
          ...defaultConfig,
          mode: 'strict' as const, // Use strict mode to prevent pattern-based fallback
          pageTypes: {
            ...defaultConfig.pageTypes,
            // Disable all page types except the one being tested
            pricing: { ...defaultConfig.pageTypes.pricing, enabled: testCase.pageType === 'pricing' },
            faq: { ...defaultConfig.pageTypes.faq, enabled: testCase.pageType === 'faq' },
            features: { ...defaultConfig.pageTypes.features, enabled: testCase.pageType === 'features' },
            about: { ...defaultConfig.pageTypes.about, enabled: testCase.pageType === 'about' }
          }
        };

        const targetedScraper = new MultiPageScraper(targetedConfig);
        const mainContent = `[Link](${testCase.url})`;
        const decision = await targetedScraper.discoverAndPlanScraping('https://example.com', mainContent);

        // Check in scrapeNow, queueForLater, and skipPages since pages might be in any category
        const allDiscoveredPages = [...decision.scrapeNow, ...decision.queueForLater, ...decision.skipPages];
        const foundPage = allDiscoveredPages.find(page => page.pageType === testCase.pageType);

        if (testCase.shouldMatch) {
          expect(foundPage).toBeDefined();
          if (foundPage) {
            expect(foundPage.url).toBe(`https://example.com${testCase.url}`);
            expect(foundPage.foundMethod).toBe('link');
          }
        } else {
          expect(foundPage).toBeUndefined();
        }
      }
    });
  });
});
