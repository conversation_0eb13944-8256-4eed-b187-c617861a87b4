/**
 * Unit Tests for Screenshot Capture
 * Tests screenshot functionality, viewport configuration, image quality, and storage mechanisms
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { MediaExtractor } from '@/lib/scraping/media-extractor';
import { ScreenshotResult, ScrapeOptions } from '@/lib/scraping/types';

// Mock the scrape-do client
const mockScrapeDoClient = {
  scrapePage: jest.fn()
};

jest.mock('@/lib/scraping/scrape-do-client', () => ({
  scrapeDoClient: mockScrapeDoClient
}));

describe('Screenshot Capture', () => {
  let mediaExtractor: MediaExtractor;

  beforeEach(() => {
    mediaExtractor = new MediaExtractor();
    jest.clearAllMocks();
  });

  describe('Standard Viewport Screenshots', () => {
    it('should capture standard viewport screenshot with correct parameters', async () => {
      const mockResponse = {
        success: true,
        content: 'Page content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'base64-encoded-screenshot-data'
        }]
      };

      mockScrapeDoClient.scrapePage.mockResolvedValue(mockResponse);

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(true);
      expect(result.screenshot).toBe('base64-encoded-screenshot-data');
      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith('https://example.com', {
        enableJSRendering: true,
        captureScreenshot: true,
        fullPageScreenshot: false,
        blockResources: true,
        timeout: 30000,
        deviceType: 'desktop',
        customWaitTime: 2000,
        returnJSON: true
      });
    });

    it('should handle screenshot capture failures', async () => {
      const mockResponse = {
        success: false,
        error: 'Screenshot capture failed',
        content: ''
      };

      mockScrapeDoClient.scrapePage.mockResolvedValue(mockResponse);

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Screenshot capture failed');
    });

    it('should handle network errors during screenshot capture', async () => {
      mockScrapeDoClient.scrapePage.mockRejectedValue(new Error('Network timeout'));

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network timeout');
    });
  });

  describe('Enhanced Screenshot Options', () => {
    it('should capture screenshots with custom device types', async () => {
      const deviceTypes = ['desktop', 'mobile', 'tablet'] as const;

      for (const deviceType of deviceTypes) {
        mockScrapeDoClient.scrapePage.mockResolvedValue({
          success: true,
          content: 'Content',
          screenShots: [{
            type: 'ScreenShot',
            image: `${deviceType}-screenshot-data`
          }]
        });

        const result = await mediaExtractor.captureScreenshot('https://example.com', {
          deviceType
        });

        expect(result.success).toBe(true);
        expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith('https://example.com', 
          expect.objectContaining({
            deviceType
          })
        );
      }
    });

    it('should capture full page screenshots when requested', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'FullScreenShot',
          image: 'full-page-screenshot-data'
        }]
      });

      const result = await mediaExtractor.captureScreenshot('https://example.com', {
        fullPage: true
      });

      expect(result.success).toBe(true);
      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith('https://example.com',
        expect.objectContaining({
          fullPageScreenshot: true
        })
      );
    });

    it('should wait for specific selectors before capturing', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'selector-waited-screenshot'
        }]
      });

      const result = await mediaExtractor.captureScreenshot('https://example.com', {
        waitForSelector: '.dynamic-content'
      });

      expect(result.success).toBe(true);
      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith('https://example.com',
        expect.objectContaining({
          waitForSelector: '.dynamic-content'
        })
      );
    });

    it('should handle custom wait times', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'custom-wait-screenshot'
        }]
      });

      const customWait = 5000;
      const result = await mediaExtractor.captureScreenshot('https://example.com', {
        customWait
      });

      expect(result.success).toBe(true);
      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith('https://example.com',
        expect.objectContaining({
          customWaitTime: customWait
        })
      );
    });
  });

  describe('Screenshot Metadata', () => {
    it('should include metadata in screenshot results', async () => {
      const mockResponse = {
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'screenshot-with-metadata'
        }]
      };

      mockScrapeDoClient.scrapePage.mockResolvedValue(mockResponse);

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(true);
      expect(result.metadata).toBeDefined();
      expect(result.metadata?.fullPage).toBe(false);
      expect(result.metadata?.capturedAt).toBeDefined();
      expect(result.timestamp).toBeDefined();
    });

    it('should handle different screenshot types in metadata', async () => {
      const testCases = [
        { type: 'ScreenShot', fullPage: false },
        { type: 'FullScreenShot', fullPage: true }
      ];

      for (const testCase of testCases) {
        mockScrapeDoClient.scrapePage.mockResolvedValue({
          success: true,
          content: 'Content',
          screenShots: [{
            type: testCase.type,
            image: 'test-screenshot'
          }]
        });

        const result = await mediaExtractor.captureScreenshot('https://example.com', {
          fullPage: testCase.fullPage
        });

        expect(result.metadata?.fullPage).toBe(testCase.fullPage);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle missing screenshot data', async () => {
      const mockResponse = {
        success: true,
        content: 'Content',
        screenShots: [] // No screenshots
      };

      mockScrapeDoClient.scrapePage.mockResolvedValue(mockResponse);

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('No screenshot data');
    });

    it('should handle malformed screenshot response', async () => {
      const mockResponse = {
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot'
          // Missing image data
        }]
      };

      mockScrapeDoClient.scrapePage.mockResolvedValue(mockResponse);

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid screenshot data');
    });

    it('should handle API errors during screenshot capture', async () => {
      const mockResponse = {
        success: false,
        error: 'API rate limit exceeded',
        content: ''
      };

      mockScrapeDoClient.scrapePage.mockResolvedValue(mockResponse);

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('API rate limit exceeded');
    });

    it('should handle timeout errors', async () => {
      mockScrapeDoClient.scrapePage.mockRejectedValue(new Error('Request timeout'));

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Request timeout');
    });
  });

  describe('Screenshot Quality and Performance', () => {
    it('should optimize performance with resource blocking', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'optimized-screenshot'
        }]
      });

      await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith('https://example.com',
        expect.objectContaining({
          blockResources: true
        })
      );
    });

    it('should use appropriate timeouts for different scenarios', async () => {
      const testCases = [
        { options: {}, expectedTimeout: 30000 },
        { options: { fullPage: true }, expectedTimeout: 45000 },
        { options: { waitForSelector: '.complex' }, expectedTimeout: 60000 }
      ];

      for (const testCase of testCases) {
        mockScrapeDoClient.scrapePage.mockResolvedValue({
          success: true,
          content: 'Content',
          screenShots: [{ type: 'ScreenShot', image: 'test' }]
        });

        await mediaExtractor.captureScreenshot('https://example.com', testCase.options);

        const lastCall = mockScrapeDoClient.scrapePage.mock.calls[
          mockScrapeDoClient.scrapePage.mock.calls.length - 1
        ];
        const options = lastCall[1] as ScrapeOptions;
        
        expect(options.timeout).toBeGreaterThanOrEqual(testCase.expectedTimeout);
      }
    });
  });

  describe('Viewport Configuration', () => {
    it('should use standard desktop viewport by default', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'desktop-screenshot'
        }]
      });

      await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(mockScrapeDoClient.scrapePage).toHaveBeenCalledWith('https://example.com',
        expect.objectContaining({
          deviceType: 'desktop'
        })
      );
    });

    it('should handle custom viewport dimensions', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'custom-viewport-screenshot'
        }]
      });

      const result = await mediaExtractor.captureScreenshot('https://example.com', {
        width: 1920,
        height: 1080
      });

      expect(result.success).toBe(true);
      // Custom dimensions should be handled by the scraping service
    });
  });

  describe('Screenshot Storage and Retrieval', () => {
    it('should return base64 encoded screenshot data', async () => {
      const base64Data = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot',
          image: base64Data
        }]
      });

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(true);
      expect(result.screenshot).toBe(base64Data);
    });

    it('should handle multiple screenshots in response', async () => {
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [
          { type: 'ScreenShot', image: 'viewport-screenshot' },
          { type: 'FullScreenShot', image: 'full-page-screenshot' }
        ]
      });

      const result = await mediaExtractor.captureViewportScreenshot('https://example.com');

      expect(result.success).toBe(true);
      expect(result.screenshot).toBe('viewport-screenshot'); // Should use first screenshot
    });
  });

  describe('Integration with Media Collection', () => {
    it('should integrate screenshot capture with media collection priority', async () => {
      // Mock no favicon or OG images
      const mockFaviconCollector = {
        collectFavicon: jest.fn().mockResolvedValue({
          faviconUrls: [],
          primaryFavicon: null,
          extractedAt: new Date().toISOString()
        })
      };

      const mockOgImageHandler = {
        extractOGImages: jest.fn().mockReturnValue([])
      };

      jest.doMock('@/lib/scraping/favicon-collector', () => ({
        faviconCollector: mockFaviconCollector
      }));

      jest.doMock('@/lib/scraping/og-image-handler', () => ({
        ogImageHandler: mockOgImageHandler
      }));

      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'fallback-screenshot'
        }]
      });

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', '<html></html>');

      expect(result.favicon).toBeNull();
      expect(result.ogImages).toHaveLength(0);
      expect(result.screenshot).toBeDefined();
      expect(result.screenshot?.success).toBe(true);
    });
  });
});
