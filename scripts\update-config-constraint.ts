#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateConfigConstraint() {
  console.log('🔧 Updating system_configuration constraint...');

  try {
    // Drop the existing constraint
    const { error: dropError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE system_configuration DROP CONSTRAINT IF EXISTS system_configuration_config_type_check;'
    });

    if (dropError) {
      console.error('Error dropping constraint:', dropError);
      return;
    }

    console.log('✅ Dropped existing constraint');

    // Add the updated constraint
    const { error: addError } = await supabase.rpc('exec_sql', {
      sql: `ALTER TABLE system_configuration ADD CONSTRAINT system_configuration_config_type_check 
            CHECK (config_type IN ('ai_provider', 'scraping', 'job_processing', 'system', 'security', 'prompt_template'));`
    });

    if (addError) {
      console.error('Error adding new constraint:', addError);
      return;
    }

    console.log('✅ Added updated constraint with prompt_template support');

    // Verify the constraint
    const { data: constraints, error: verifyError } = await supabase.rpc('exec_sql', {
      sql: `SELECT conname, consrc 
            FROM pg_constraint 
            WHERE conrelid = 'system_configuration'::regclass 
            AND conname = 'system_configuration_config_type_check';`
    });

    if (verifyError) {
      console.error('Error verifying constraint:', verifyError);
    } else {
      console.log('✅ Constraint verification:', constraints);
    }

    console.log('🎉 Database constraint update completed!');

  } catch (error) {
    console.error('Error updating constraint:', error);
    process.exit(1);
  }
}

// Run the update
updateConfigConstraint().catch(console.error);
