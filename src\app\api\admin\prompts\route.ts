import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: 'content' | 'description' | 'features' | 'pricing' | 'pros_cons';
  promptType: 'system' | 'user'; // System prompts for validation/formatting, User prompts for content generation
  template: string;
  variables: string[];
  isActive: boolean;
  lastModified: string;
  usage: number;
  validationRules?: string[]; // For system prompts
  formatRequirements?: string; // For system prompts
}

/**
 * GET /api/admin/prompts
 * Get all prompt templates
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get prompts from system_configuration table
    const { data: configData, error } = await supabase
      .from('system_configuration')
      .select('*')
      .eq('config_type', 'prompt_template')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching prompts:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch prompts' },
        { status: 500 }
      );
    }

    // Transform database records to PromptTemplate format
    const prompts: PromptTemplate[] = configData.map(record => ({
      id: record.id,
      name: record.config_value.name || record.config_key,
      description: record.config_value.description || '',
      category: record.config_value.category || 'content',
      promptType: record.config_value.promptType || 'user', // Default to user prompt
      template: record.config_value.template || '',
      variables: record.config_value.variables || [],
      isActive: record.is_active,
      lastModified: record.updated_at || record.created_at,
      usage: record.config_value.usage || 0,
      validationRules: record.config_value.validationRules || [],
      formatRequirements: record.config_value.formatRequirements || ''
    }));

    return NextResponse.json({
      success: true,
      data: prompts
    });

  } catch (error) {
    console.error('Prompts API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/prompts
 * Create or update a prompt template
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, data } = body;

    if (action === 'create') {
      // Create new prompt template
      const promptData = {
        config_key: `prompt_${data.category}_${Date.now()}`,
        config_value: {
          name: data.name,
          description: data.description,
          category: data.category,
          promptType: data.promptType || 'user',
          template: data.template,
          variables: data.variables || [],
          validationRules: data.validationRules || [],
          formatRequirements: data.formatRequirements || '',
          usage: 0
        },
        config_type: 'prompt_template',
        is_sensitive: false,
        is_active: data.isActive !== false,
        description: `Prompt template: ${data.name}`,
        updated_by: 'admin'
      };

      const { data: newPrompt, error } = await supabase
        .from('system_configuration')
        .insert(promptData)
        .select()
        .single();

      if (error) {
        console.error('Error creating prompt:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to create prompt' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          id: newPrompt.id,
          ...promptData.config_value,
          isActive: newPrompt.is_active,
          lastModified: newPrompt.created_at
        }
      });

    } else if (action === 'update') {
      // Update existing prompt template
      const { id, ...updateData } = data;

      const { data: existingPrompt, error: fetchError } = await supabase
        .from('system_configuration')
        .select('config_value')
        .eq('id', id)
        .single();

      if (fetchError) {
        return NextResponse.json(
          { success: false, error: 'Prompt not found' },
          { status: 404 }
        );
      }

      const updatedConfigValue = {
        ...existingPrompt.config_value,
        ...updateData,
        usage: existingPrompt.config_value.usage || 0
      };

      const { data: updatedPrompt, error } = await supabase
        .from('system_configuration')
        .update({
          config_value: updatedConfigValue,
          is_active: updateData.isActive !== false,
          updated_by: 'admin',
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating prompt:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to update prompt' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          id: updatedPrompt.id,
          ...updatedConfigValue,
          isActive: updatedPrompt.is_active,
          lastModified: updatedPrompt.updated_at
        }
      });

    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action. Use "create" or "update"' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Prompts API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/prompts
 * Delete a prompt template
 */
export async function DELETE(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Prompt ID is required' },
        { status: 400 }
      );
    }

    // Soft delete by setting is_active to false
    const { error } = await supabase
      .from('system_configuration')
      .update({
        is_active: false,
        updated_by: 'admin',
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('config_type', 'prompt_template');

    if (error) {
      console.error('Error deleting prompt:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete prompt' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Prompt template deleted successfully'
    });

  } catch (error) {
    console.error('Prompts DELETE API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
