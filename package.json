{"name": "ai-dude-directory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "tsx scripts/migrate-data.ts", "db:setup": "npm run migrate", "db:migrate": "tsx src/lib/database/migrate.ts up", "db:migrate:002": "tsx scripts/run-migration-002.ts", "db:migrate:003": "tsx scripts/apply-uniqueness-constraints-migration.ts", "db:migrate:versioning": "tsx scripts/run-versioning-migration.ts", "db:rollback": "tsx src/lib/database/migrate.ts down", "db:status": "tsx scripts/check-migration-status.ts", "db:show-sql": "tsx scripts/show-migration-sql.ts", "db:test-pg-trgm": "tsx scripts/test-pg-trgm-migration.ts", "test:jobs": "tsx scripts/test-jobs.ts", "test:automation": "tsx scripts/test-core-automation.ts", "test:e2e": "tsx tests/e2e/admin-workflows.test.ts", "health:check": "tsx scripts/production-health-check.ts", "inspect:data": "tsx scripts/inspect-scraped-data.ts", "demo:scraping": "tsx scripts/demo-scraping.ts", "production:verify": "npm run health:check && npm run test:e2e", "deploy:prepare": "npm run build && npm run production:verify", "monitor:start": "tsx scripts/production-monitor.ts", "cleanup:jobs": "tsx scripts/cleanup-completed-jobs.ts", "test:ai": "tsx src/lib/ai/test-dual-providers.ts", "ai:health": "tsx -e \"import { performHealthCheck } from './src/lib/ai'; performHealthCheck().then(console.log).catch(console.error)\"", "ai:validate": "tsx scripts/validate-ai-config.ts", "test:config": "tsx scripts/test-config-api.ts", "type:check": "node scripts/check-types.js", "type:validate": "tsx scripts/check-bulk-processing-types.ts", "migrate:execute": "tsx scripts/execute-data-migration.ts", "migrate:status": "tsx scripts/execute-data-migration.ts status", "migrate:rollback": "tsx scripts/execute-data-migration.ts rollback", "test:comprehensive": "tsx tests/comprehensive-test-runner.ts", "test:integration": "tsx tests/integration/enhanced-ai-system.test.ts", "test:performance": "tsx tests/performance/load-testing.ts", "test:ai-providers": "tsx tests/api/ai-providers.test.ts", "test:rollback": "tsx tests/migration/rollback-validation.test.ts", "test:system": "tsx tests/system-validation.ts", "setup:test-env": "tsx scripts/setup-test-environment.ts", "migrate:dry-run": "tsx scripts/execute-data-migration.ts execute --dry-run", "seed:tools": "tsx scripts/seed-sample-tools.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:admin": "jest --testPathPattern=admin"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.49.10", "@types/animejs": "^3.1.13", "@types/jsonwebtoken": "^9.0.9", "animejs": "^4.0.2", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "15.3.2", "nodemailer": "^6.9.8", "openai": "^5.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "zod": "^3.25.64"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.3", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.14", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^30.0.0", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^30.0.1", "jest-environment-jsdom": "^30.0.0", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5", "undici": "^7.10.0"}}