// Configuration Management API Endpoints

import { NextRequest, NextResponse } from 'next/server';
import { adminConfigService } from '../../../../lib/config/admin-config';
import { configManager } from '../../../../lib/config/configuration-manager';
import { ValidationResult } from '../../../../lib/config/types';

// Middleware to check admin authentication
function checkAdminAuth(request: NextRequest): boolean {
  const adminApiKey = request.headers.get('x-admin-api-key');
  const expectedKey = process.env.ADMIN_API_KEY;
  
  return adminApiKey === expectedKey && expectedKey !== undefined;
}

// GET /api/admin/config - Get current configuration
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');
    const includeSecrets = searchParams.get('includeSecrets') === 'true';

    // Wait for configuration manager to be ready
    await configManager.waitForInitialization();

    let response: Record<string, unknown>;

    switch (section) {
      case 'ai-providers':
        response = {
          section: 'ai-providers',
          data: adminConfigService.getAIProviderConfig()
        };
        break;

      case 'scraping':
        response = {
          section: 'scraping',
          data: adminConfigService.getScrapingConfig()
        };
        break;

      case 'system':
        response = {
          section: 'system',
          data: adminConfigService.getSystemConfig()
        };
        break;

      case 'summary':
        response = {
          section: 'summary',
          data: adminConfigService.getConfigurationSummary()
        };
        break;

      case 'export':
        const exportData = await adminConfigService.exportConfiguration(includeSecrets);
        return new NextResponse(exportData, {
          headers: {
            'Content-Type': 'application/json',
            'Content-Disposition': `attachment; filename="config-export-${new Date().toISOString().split('T')[0]}.json"`
          }
        });

      default:
        // Return all configuration sections
        response = {
          section: 'all',
          data: {
            aiProviders: adminConfigService.getAIProviderConfig(),
            scraping: adminConfigService.getScrapingConfig(),
            system: adminConfigService.getSystemConfig(),
            summary: adminConfigService.getConfigurationSummary()
          }
        };
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Configuration GET error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to retrieve configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/config - Update configuration
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { section, data, action } = body;

    // Wait for configuration manager to be ready
    await configManager.waitForInitialization();

    let result: ValidationResult | { success: boolean; message: string };

    switch (action) {
      case 'update':
        switch (section) {
          case 'ai-providers':
            result = await adminConfigService.updateAIProviderConfig(data);
            break;

          case 'scraping':
            result = await adminConfigService.updateScrapingConfig(data);
            break;

          case 'system':
            result = await adminConfigService.updateSystemConfig(data);
            break;

          default:
            return NextResponse.json(
              { error: `Unknown configuration section: ${section}` },
              { status: 400 }
            );
        }
        break;

      case 'import':
        if (!data.configJson) {
          return NextResponse.json(
            { error: 'Configuration JSON is required for import' },
            { status: 400 }
          );
        }
        result = await adminConfigService.importConfiguration(data.configJson);
        break;

      case 'rollback':
        if (!data.changeId) {
          return NextResponse.json(
            { error: 'Change ID is required for rollback' },
            { status: 400 }
          );
        }
        await adminConfigService.rollbackChange(data.changeId);
        result = { success: true, message: 'Configuration rolled back successfully' };
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    if (result && 'isValid' in result && !result.isValid) {
      return NextResponse.json(
        {
          error: 'Configuration validation failed',
          validation: result
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Configuration updated successfully',
      validation: result
    });

  } catch (error) {
    console.error('Configuration POST error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/admin/config - Set specific configuration value
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { path, value } = body;

    if (!path) {
      return NextResponse.json(
        { error: 'Configuration path is required' },
        { status: 400 }
      );
    }

    // Wait for configuration manager to be ready
    await configManager.waitForInitialization();

    // Validate the new value
    const validation = await configManager.validatePath(path, value);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Configuration validation failed',
          validation
        },
        { status: 400 }
      );
    }

    // Set the configuration value
    await configManager.set(path, value);

    return NextResponse.json({
      success: true,
      message: `Configuration ${path} updated successfully`,
      validation
    });

  } catch (error) {
    console.error('Configuration PUT error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to set configuration value',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/config - Delete configuration value
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');

    if (!path) {
      return NextResponse.json(
        { error: 'Configuration path is required' },
        { status: 400 }
      );
    }

    // Wait for configuration manager to be ready
    await configManager.waitForInitialization();

    // Delete the configuration value
    await configManager.delete(path);

    return NextResponse.json({
      success: true,
      message: `Configuration ${path} deleted successfully`
    });

  } catch (error) {
    console.error('Configuration DELETE error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete configuration value',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
