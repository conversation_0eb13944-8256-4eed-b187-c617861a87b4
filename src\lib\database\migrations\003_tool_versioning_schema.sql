-- Tool Versioning System Database Schema Migration (003)
-- Implements comprehensive version control for tool changes with rollback capabilities
-- Follows snapshot-based versioning pattern for reliable rollback operations
--
-- OVERVIEW:
-- This migration creates a complete version control system for tools with:
-- - Snapshot-based versioning using JSONB storage
-- - Comprehensive audit trail for all versioning operations
-- - Performance-optimized version comparison caching
-- - Automatic version numbering and current version management
-- - Database triggers for data integrity and cleanup
--
-- TABLES CREATED:
-- 1. tool_versions: Complete tool snapshots for each version
-- 2. version_audit_log: Audit trail for versioning operations
-- 3. version_comparisons: Cached comparison results for performance
--
-- FEATURES:
-- - Rollback to any previous version with full audit trail
-- - Change tracking with user attribution and timestamps
-- - Performance optimization through comparison caching
-- - Data integrity enforcement through constraints and triggers
-- - Automatic cleanup of expired cache entries
--
-- INTEGRATION:
-- - Integrates with existing tools table via foreign keys
-- - Cascade deletes ensure data consistency
-- - Compatible with audit trail system for comprehensive logging
--
-- PERFORMANCE:
-- - Comprehensive indexing for fast queries
-- - GIN indexes for JSONB data search
-- - Partial indexes for current versions
-- - Cached comparisons reduce computation overhead
--
-- SECURITY:
-- - Complete audit trail for all operations
-- - User attribution for all changes
-- - IP and session tracking for security monitoring
-- - Rollback reason tracking for accountability

-- Tool Versions Table
-- Stores complete snapshots of tool data for each version
CREATE TABLE IF NOT EXISTS tool_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_id VARCHAR(255) NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    version_data JSONB NOT NULL, -- Complete tool snapshot
    change_summary TEXT, -- Brief description of changes
    created_by VARCHAR(255) NOT NULL, -- User who made the change
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Metadata
    is_current BOOLEAN DEFAULT FALSE, -- Marks the current active version
    change_type VARCHAR(50) DEFAULT 'update' CHECK (change_type IN ('create', 'update', 'rollback', 'bulk_update')),
    change_source VARCHAR(50) DEFAULT 'admin_panel' CHECK (change_source IN ('admin_panel', 'api', 'bulk_import', 'automation')),
    
    -- Version control metadata
    parent_version_id UUID REFERENCES tool_versions(id), -- Previous version reference
    rollback_reason TEXT, -- Reason for rollback (if applicable)
    
    -- Constraints
    UNIQUE(tool_id, version_number),
    CHECK (version_number > 0)
);

-- Version Audit Log Table
-- Tracks all versioning actions for comprehensive audit trail
CREATE TABLE IF NOT EXISTS version_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_id VARCHAR(255) NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    version_id UUID REFERENCES tool_versions(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL CHECK (action IN ('create_version', 'rollback', 'compare', 'view', 'delete_version')),
    performed_by VARCHAR(255) NOT NULL, -- User who performed the action
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Action details
    action_details JSONB, -- Additional context about the action
    ip_address INET, -- User's IP address
    user_agent TEXT, -- User's browser/client info
    
    -- Rollback specific fields
    target_version_number INTEGER, -- Version being rolled back to
    rollback_reason TEXT, -- Reason for rollback
    
    -- System metadata
    session_id VARCHAR(255), -- User session identifier
    request_id VARCHAR(255) -- Request tracking ID
);

-- Version Comparison Cache Table
-- Caches version comparison results for performance
CREATE TABLE IF NOT EXISTS version_comparisons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_id VARCHAR(255) NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    from_version_id UUID NOT NULL REFERENCES tool_versions(id) ON DELETE CASCADE,
    to_version_id UUID NOT NULL REFERENCES tool_versions(id) ON DELETE CASCADE,
    comparison_data JSONB NOT NULL, -- Diff results
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Cache management
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours'),
    
    -- Constraints
    UNIQUE(from_version_id, to_version_id)
);

-- Indexes for Performance
-- Tool versions indexes
CREATE INDEX IF NOT EXISTS idx_tool_versions_tool_id ON tool_versions(tool_id);
CREATE INDEX IF NOT EXISTS idx_tool_versions_created_at ON tool_versions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_tool_versions_current ON tool_versions(tool_id, is_current) WHERE is_current = TRUE;
CREATE INDEX IF NOT EXISTS idx_tool_versions_version_number ON tool_versions(tool_id, version_number DESC);
CREATE INDEX IF NOT EXISTS idx_tool_versions_created_by ON tool_versions(created_by);

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_version_audit_tool_id ON version_audit_log(tool_id);
CREATE INDEX IF NOT EXISTS idx_version_audit_performed_at ON version_audit_log(performed_at DESC);
CREATE INDEX IF NOT EXISTS idx_version_audit_action ON version_audit_log(action);
CREATE INDEX IF NOT EXISTS idx_version_audit_performed_by ON version_audit_log(performed_by);

-- Comparison cache indexes
CREATE INDEX IF NOT EXISTS idx_version_comparisons_tool_id ON version_comparisons(tool_id);
CREATE INDEX IF NOT EXISTS idx_version_comparisons_expires ON version_comparisons(expires_at);

-- GIN indexes for JSONB fields
CREATE INDEX IF NOT EXISTS idx_tool_versions_data_gin ON tool_versions USING GIN(version_data);
CREATE INDEX IF NOT EXISTS idx_version_audit_details_gin ON version_audit_log USING GIN(action_details);
CREATE INDEX IF NOT EXISTS idx_version_comparisons_data_gin ON version_comparisons USING GIN(comparison_data);

-- Functions and Triggers

-- Function to automatically increment version numbers
CREATE OR REPLACE FUNCTION increment_version_number()
RETURNS TRIGGER AS $$
BEGIN
    -- Get the next version number for this tool
    SELECT COALESCE(MAX(version_number), 0) + 1
    INTO NEW.version_number
    FROM tool_versions
    WHERE tool_id = NEW.tool_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-increment version numbers
DROP TRIGGER IF EXISTS trigger_increment_version_number ON tool_versions;
CREATE TRIGGER trigger_increment_version_number
    BEFORE INSERT ON tool_versions
    FOR EACH ROW
    WHEN (NEW.version_number IS NULL)
    EXECUTE FUNCTION increment_version_number();

-- Function to ensure only one current version per tool
CREATE OR REPLACE FUNCTION ensure_single_current_version()
RETURNS TRIGGER AS $$
BEGIN
    -- If this version is being marked as current
    IF NEW.is_current = TRUE THEN
        -- Mark all other versions of this tool as not current
        UPDATE tool_versions 
        SET is_current = FALSE 
        WHERE tool_id = NEW.tool_id AND id != NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to ensure single current version
DROP TRIGGER IF EXISTS trigger_ensure_single_current_version ON tool_versions;
CREATE TRIGGER trigger_ensure_single_current_version
    AFTER INSERT OR UPDATE ON tool_versions
    FOR EACH ROW
    WHEN (NEW.is_current = TRUE)
    EXECUTE FUNCTION ensure_single_current_version();

-- Function to clean up expired comparison cache
CREATE OR REPLACE FUNCTION cleanup_expired_comparisons()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM version_comparisons 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Add version tracking to existing tools table
ALTER TABLE tools ADD COLUMN IF NOT EXISTS current_version_id UUID REFERENCES tool_versions(id);
ALTER TABLE tools ADD COLUMN IF NOT EXISTS version_count INTEGER DEFAULT 0;

-- Create index for version tracking in tools table
CREATE INDEX IF NOT EXISTS idx_tools_current_version ON tools(current_version_id);

-- Initial data setup
-- This will be handled by the application layer to create initial versions for existing tools
