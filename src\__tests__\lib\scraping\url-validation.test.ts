/**
 * Unit Tests for URL Validation and Sanitization
 * Tests URL validation logic, sanitization functions, and edge cases for malformed URLs
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';

// Mock environment variables
const mockEnv = {
  SCRAPE_DO_API_KEY: 'test-api-key',
  SCRAPE_DO_BASE_URL: 'https://api.scrape.do'
};

// Mock process.env
Object.defineProperty(process, 'env', {
  value: { ...process.env, ...mockEnv }
});

// Import after mocking environment
import { ScrapeDoClient } from '@/lib/scraping/scrape-do-client';

describe('URL Validation and Sanitization', () => {
  let scrapeDoClient: ScrapeDoClient;

  beforeEach(() => {
    scrapeDoClient = new ScrapeDoClient();
    jest.clearAllMocks();
  });

  describe('URL Validation', () => {
    it('should validate correct HTTP URLs', () => {
      const validUrls = [
        'http://example.com',
        'https://example.com',
        'https://www.example.com',
        'https://subdomain.example.com',
        'https://example.com/path',
        'https://example.com/path?query=value',
        'https://example.com/path#fragment',
        'https://example.com:8080/path'
      ];

      validUrls.forEach(url => {
        expect(() => new URL(url)).not.toThrow();
      });
    });

    it('should reject invalid URLs', () => {
      const invalidUrls = [
        '',
        'not-a-url',
        'ftp://example.com',
        'file:///path/to/file',
        'javascript:alert("xss")',
        'data:text/html,<script>alert("xss")</script>',
        'mailto:<EMAIL>',
        'tel:+1234567890',
        'http://',
        'https://',
        'http://.',
        'http://..',
        'http://../',
        'http://?',
        'http://??/',
        'http://#',
        'http://##/',
        'http:// shouldfail.com',
        'http://-error-.invalid/',
        'http://a.b--c.de/',
        'http://-a.b.co',
        'http://a.b-.co'
      ];

      invalidUrls.forEach(url => {
        expect(() => new URL(url)).toThrow();
      });
    });

    it('should validate URLs with special characters', () => {
      const specialUrls = [
        'https://example.com/path with spaces',
        'https://example.com/path%20with%20encoded%20spaces',
        'https://example.com/path?query=value with spaces',
        'https://example.com/path?query=value%20with%20encoded%20spaces',
        'https://example.com/path#fragment with spaces',
        'https://example.com/path#fragment%20with%20encoded%20spaces',
        'https://example.com/path/to/file.html?param1=value1&param2=value2',
        'https://example.com/search?q=test+query&sort=date&limit=10'
      ];

      specialUrls.forEach(url => {
        expect(() => new URL(url)).not.toThrow();
      });
    });

    it('should handle international domain names', () => {
      const internationalUrls = [
        'https://example.co.uk',
        'https://example.com.au',
        'https://example.de',
        'https://example.jp',
        'https://example.中国',
        'https://пример.рф'
      ];

      internationalUrls.forEach(url => {
        expect(() => new URL(url)).not.toThrow();
      });
    });
  });

  describe('URL Sanitization', () => {
    it('should sanitize URLs by removing dangerous protocols', () => {
      const dangerousUrls = [
        'javascript:alert("xss")',
        'data:text/html,<script>alert("xss")</script>',
        'vbscript:msgbox("xss")',
        'file:///etc/passwd'
      ];

      dangerousUrls.forEach(url => {
        expect(() => new URL(url)).toThrow();
      });
    });

    it('should normalize URL components', () => {
      const testCases = [
        {
          input: 'HTTPS://EXAMPLE.COM/PATH',
          expected: 'https://example.com/PATH'
        },
        {
          input: 'https://example.com//double//slash//path',
          expected: 'https://example.com/double/slash/path'
        },
        {
          input: 'https://example.com/path/../parent',
          expected: 'https://example.com/parent'
        },
        {
          input: 'https://example.com/path/./current',
          expected: 'https://example.com/path/current'
        }
      ];

      testCases.forEach(({ input, expected }) => {
        const url = new URL(input);
        expect(url.href.toLowerCase().includes(expected.toLowerCase())).toBe(true);
      });
    });

    it('should handle URL encoding properly', () => {
      const testCases = [
        {
          input: 'https://example.com/search?q=hello world',
          shouldContain: 'hello%20world'
        },
        {
          input: 'https://example.com/path with spaces',
          shouldContain: 'path%20with%20spaces'
        }
      ];

      testCases.forEach(({ input, shouldContain }) => {
        const url = new URL(input);
        expect(url.href).toContain(shouldContain);
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle extremely long URLs', () => {
      const longPath = 'a'.repeat(2000);
      const longUrl = `https://example.com/${longPath}`;
      
      expect(() => new URL(longUrl)).not.toThrow();
    });

    it('should handle URLs with many query parameters', () => {
      const queryParams = Array.from({ length: 100 }, (_, i) => `param${i}=value${i}`).join('&');
      const urlWithManyParams = `https://example.com/path?${queryParams}`;
      
      expect(() => new URL(urlWithManyParams)).not.toThrow();
    });

    it('should handle URLs with special port numbers', () => {
      const portsToTest = [80, 443, 8080, 3000, 8443, 9000];
      
      portsToTest.forEach(port => {
        const url = `https://example.com:${port}/path`;
        expect(() => new URL(url)).not.toThrow();
      });
    });

    it('should handle IPv4 addresses', () => {
      const ipUrls = [
        'http://***********',
        'https://********:8080',
        'http://127.0.0.1:3000/path'
      ];

      ipUrls.forEach(url => {
        expect(() => new URL(url)).not.toThrow();
      });
    });

    it('should handle IPv6 addresses', () => {
      const ipv6Urls = [
        'http://[::1]',
        'https://[2001:db8::1]:8080',
        'http://[fe80::1%lo0]/path'
      ];

      ipv6Urls.forEach(url => {
        expect(() => new URL(url)).not.toThrow();
      });
    });
  });

  describe('Security Validation', () => {
    it('should reject URLs with suspicious patterns', () => {
      const suspiciousUrls = [
        'javascript:void(0)',
        'data:text/html;base64,PHNjcmlwdD5hbGVydCgneHNzJyk8L3NjcmlwdD4=',
        'vbscript:CreateObject("WScript.Shell").Run("calc")'
      ];

      suspiciousUrls.forEach(url => {
        expect(() => new URL(url)).toThrow();
      });
    });

    it('should validate against common XSS patterns', () => {
      const xssPatterns = [
        'javascript:alert(1)',
        'data:text/html,<img src=x onerror=alert(1)>',
        'javascript:eval(atob("YWxlcnQoMSk="))'
      ];

      xssPatterns.forEach(pattern => {
        expect(() => new URL(pattern)).toThrow();
      });
    });
  });

  describe('URL Parsing Edge Cases', () => {
    it('should handle malformed query strings', () => {
      const malformedQueries = [
        'https://example.com/path?=value',
        'https://example.com/path?key=',
        'https://example.com/path?key1=value1&=value2',
        'https://example.com/path?key1=value1&key2='
      ];

      malformedQueries.forEach(url => {
        expect(() => new URL(url)).not.toThrow();
      });
    });

    it('should handle malformed fragments', () => {
      const malformedFragments = [
        'https://example.com/path#',
        'https://example.com/path##',
        'https://example.com/path#fragment with spaces'
      ];

      malformedFragments.forEach(url => {
        expect(() => new URL(url)).not.toThrow();
      });
    });
  });
});
