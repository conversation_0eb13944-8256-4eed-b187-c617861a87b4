'use client';

import React, { useState, useEffect } from 'react';
import {
  Layers,
  Play,
  Pause,
  RotateCcw,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  Filter,
  Search,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface QueueItem {
  id: string;
  toolId: string;
  toolName: string;
  url: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'paused';
  priority: 'low' | 'medium' | 'high';
  progress: number;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  estimatedTime?: string;
  errorMessage?: string;
  retryCount: number;
  maxRetries: number;
}

export default function GenerationQueuePage() {
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  // Load queue data with retry mechanism
  const loadQueue = async (attempt = 0) => {
    try {
      setIsLoading(true);
      setError(null);
        
        // Call automation jobs API for consistency
        const response = await fetch('/api/automation/jobs', {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to load queue data');
        }

        const data = await response.json();

        if (data.success && data.data && data.data.jobs) {
          // Transform job data to queue item format
          const transformedItems: QueueItem[] = data.data.jobs.map((job: any) => ({
            id: job.id,
            toolId: job.toolId || job.metadata?.toolId || `tool-${job.id}`,
            toolName: job.toolName || job.metadata?.toolName || `Job ${job.id}`,
            url: job.url || job.metadata?.url || 'https://example.com',
            status: job.status === 'active' ? 'processing' :
                   job.status === 'waiting' ? 'pending' :
                   job.status === 'completed' ? 'completed' :
                   job.status === 'failed' ? 'failed' : 'pending',
            priority: job.priority || 'medium',
            progress: job.progress || (job.status === 'completed' ? 100 :
                     job.status === 'active' ? 50 : 0),
            createdAt: job.createdAt || job.timestamp,
            startedAt: job.startedAt,
            completedAt: job.completedAt,
            estimatedTime: job.estimatedTime ||
                          (job.status === 'completed' ? '0 min' :
                           job.status === 'failed' ? 'Failed' : '5 min'),
            retryCount: job.retryCount || 0,
            maxRetries: job.maxRetries || 3,
            errorMessage: job.failedReason || job.error
          }));

          setQueue(transformedItems);
        } else {
          throw new Error(data.error || 'Failed to load queue data');
        }
      } catch (err) {
        console.error('Error loading queue:', err);

        // Enhanced error handling with user-friendly messages
        let errorMessage = 'Failed to load queue data';
        if (err instanceof Error) {
          if (err.message.includes('401') || err.message.includes('Unauthorized')) {
            errorMessage = 'Authentication failed. Please check your admin credentials.';
          } else if (err.message.includes('404')) {
            errorMessage = 'Queue service is not available. Please contact support.';
          } else if (err.message.includes('500')) {
            errorMessage = 'Server error occurred. Please try again later.';
          } else if (err.message.includes('Failed to fetch')) {
            errorMessage = 'Network error. Please check your connection and try again.';
          }
        }

        // Show error to user but still provide fallback data
        console.warn(`Queue loading failed: ${errorMessage}`);
        setError(errorMessage);

        // Retry logic for network errors
        if (attempt < maxRetries && (err instanceof Error && err.message.includes('Failed to fetch'))) {
          console.log(`Retrying queue load (attempt ${attempt + 1}/${maxRetries})...`);
          setTimeout(() => {
            setRetryCount(attempt + 1);
            loadQueue(attempt + 1);
          }, Math.pow(2, attempt) * 1000); // Exponential backoff
          return;
        }

        // Set fallback data if API fails
        setQueue([
          {
            id: 'fallback-1',
            toolId: 'fallback-tool-1',
            toolName: 'No active jobs (API unavailable)',
            url: 'https://example.com',
            status: 'pending',
            priority: 'low',
            progress: 0,
            createdAt: new Date().toISOString(),
            estimatedTime: 'N/A',
            retryCount: 0,
            maxRetries: 3
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

  // Load queue data
  useEffect(() => {
    loadQueue();

    // Set up polling for real-time updates
    const interval = setInterval(loadQueue, 5000);
    return () => clearInterval(interval);
  }, []);

  // Filter queue items
  const filteredQueue = queue.filter(item => {
    const matchesSearch = item.toolName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.url.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || item.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-400 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      case 'paused':
        return <Pause className="w-4 h-4 text-gray-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return 'text-yellow-400';
      case 'completed':
        return 'text-green-400';
      case 'failed':
        return 'text-red-400';
      case 'paused':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-400';
      case 'medium':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === filteredQueue.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredQueue.map(item => item.id));
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedItems.length === 0) return;
    
    setIsProcessing(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update queue based on action
      setQueue(prev => prev.map(item => {
        if (selectedItems.includes(item.id)) {
          switch (action) {
            case 'retry':
              return { ...item, status: 'pending' as const, retryCount: item.retryCount + 1 };
            case 'pause':
              return { ...item, status: 'paused' as const };
            case 'resume':
              return { ...item, status: 'pending' as const };
            case 'cancel':
              return { ...item, status: 'failed' as const };
            default:
              return item;
          }
        }
        return item;
      }));
      
      setSelectedItems([]);
    } catch (err) {
      console.error('Bulk action failed:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveCompleted = async () => {
    setIsProcessing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setQueue(prev => prev.filter(item => item.status !== 'completed'));
    } catch (err) {
      console.error('Failed to remove completed items:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading generation queue...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="bg-red-900 border border-red-700 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-400" />
              <div>
                <h3 className="text-red-200 font-medium">Queue Loading Error</h3>
                <p className="text-red-300 text-sm">{error}</p>
                {retryCount > 0 && (
                  <p className="text-red-400 text-xs mt-1">Retry attempt: {retryCount}/{maxRetries}</p>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => loadQueue(0)}
                className="bg-red-800 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Retry
              </button>
              <button
                onClick={() => setError(null)}
                className="text-red-400 hover:text-red-300"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Generation Queue</h1>
          <p className="text-gray-400">Monitor and manage content generation jobs</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleRemoveCompleted}
            disabled={isProcessing}
            className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Trash2 className="w-4 h-4" />
            <span>Clear Completed</span>
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Jobs</p>
              <p className="text-xl font-bold text-white">{queue.length}</p>
            </div>
            <Layers className="w-6 h-6 text-blue-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Processing</p>
              <p className="text-xl font-bold text-white">
                {queue.filter(item => item.status === 'processing').length}
              </p>
            </div>
            <Clock className="w-6 h-6 text-yellow-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Completed</p>
              <p className="text-xl font-bold text-white">
                {queue.filter(item => item.status === 'completed').length}
              </p>
            </div>
            <CheckCircle className="w-6 h-6 text-green-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Failed</p>
              <p className="text-xl font-bold text-white">
                {queue.filter(item => item.status === 'failed').length}
              </p>
            </div>
            <AlertCircle className="w-6 h-6 text-red-400" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-zinc-800 border border-black rounded-lg p-4">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search tools..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-zinc-700 border border-zinc-600 rounded-lg pl-10 pr-4 py-2 text-white focus:outline-none focus:border-blue-500"
              />
            </div>
          </div>
          
          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="paused">Paused</option>
          </select>
          
          {/* Priority Filter */}
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
          >
            <option value="all">All Priority</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="bg-blue-900 border border-blue-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-blue-200">
              {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} selected
            </span>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBulkAction('retry')}
                disabled={isProcessing}
                className="bg-green-700 hover:bg-green-600 disabled:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Retry
              </button>
              <button
                onClick={() => handleBulkAction('pause')}
                disabled={isProcessing}
                className="bg-yellow-700 hover:bg-yellow-600 disabled:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Pause
              </button>
              <button
                onClick={() => handleBulkAction('cancel')}
                disabled={isProcessing}
                className="bg-red-700 hover:bg-red-600 disabled:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Queue Table */}
      <div className="bg-zinc-800 border border-black rounded-lg overflow-hidden">
        <div className="p-4 border-b border-zinc-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">Queue Items</h2>
            <button
              onClick={handleSelectAll}
              className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
            >
              {selectedItems.length === filteredQueue.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          {filteredQueue.length === 0 ? (
            <div className="text-center py-8">
              <Layers className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">No items in queue</p>
            </div>
          ) : (
            <table className="w-full">
              <thead className="bg-zinc-700">
                <tr>
                  <th className="text-left p-4">
                    <input
                      type="checkbox"
                      checked={selectedItems.length === filteredQueue.length && filteredQueue.length > 0}
                      onChange={handleSelectAll}
                      className="rounded"
                    />
                  </th>
                  <th className="text-left p-4 text-gray-300">Tool</th>
                  <th className="text-left p-4 text-gray-300">Status</th>
                  <th className="text-left p-4 text-gray-300">Priority</th>
                  <th className="text-left p-4 text-gray-300">Progress</th>
                  <th className="text-left p-4 text-gray-300">Created</th>
                  <th className="text-left p-4 text-gray-300">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredQueue.map((item) => (
                  <tr key={item.id} className="border-t border-zinc-700 hover:bg-zinc-700">
                    <td className="p-4">
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(item.id)}
                        onChange={() => handleSelectItem(item.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="p-4">
                      <div>
                        <h3 className="font-medium text-white">{item.toolName}</h3>
                        <p className="text-sm text-gray-400 truncate max-w-xs">{item.url}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(item.status)}
                        <span className={`text-sm font-medium capitalize ${getStatusColor(item.status)}`}>
                          {item.status}
                        </span>
                      </div>
                      {item.errorMessage && (
                        <p className="text-xs text-red-400 mt-1">{item.errorMessage}</p>
                      )}
                    </td>
                    <td className="p-4">
                      <span className={`text-sm font-medium uppercase ${getPriorityColor(item.priority)}`}>
                        {item.priority}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="w-full bg-zinc-600 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${item.progress}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-400 mt-1">{item.progress}%</span>
                    </td>
                    <td className="p-4">
                      <span className="text-sm text-gray-400">
                        {new Date(item.createdAt).toLocaleString()}
                      </span>
                      {item.estimatedTime && (
                        <p className="text-xs text-gray-500">ETA: {item.estimatedTime}</p>
                      )}
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        {item.status === 'failed' && item.retryCount < item.maxRetries && (
                          <button
                            onClick={() => handleBulkAction('retry')}
                            className="text-green-400 hover:text-green-300 transition-colors"
                            title="Retry"
                          >
                            <RotateCcw className="w-4 h-4" />
                          </button>
                        )}
                        {item.status === 'processing' && (
                          <button
                            onClick={() => handleBulkAction('pause')}
                            className="text-yellow-400 hover:text-yellow-300 transition-colors"
                            title="Pause"
                          >
                            <Pause className="w-4 h-4" />
                          </button>
                        )}
                        {item.status === 'paused' && (
                          <button
                            onClick={() => handleBulkAction('resume')}
                            className="text-green-400 hover:text-green-300 transition-colors"
                            title="Resume"
                          >
                            <Play className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
}
