/**
 * Unit Tests for Error Handling and Fallback Mechanisms
 * Tests retry logic, error classification, fallback strategies, and recovery mechanisms
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ScrapeDoClient } from '@/lib/scraping/scrape-do-client';
import { CommonErrorHandlers, ErrorHandlingUtils } from '@/lib/error-handling';
import { contentProcessor } from '@/lib/scraping/content-processor';

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch as any;

// Mock environment variables
Object.defineProperty(process, 'env', {
  value: { 
    ...process.env, 
    SCRAPE_DO_API_KEY: 'test-api-key',
    SCRAPE_DO_BASE_URL: 'https://api.scrape.do'
  }
});

describe('Error Handling and Fallback Mechanisms', () => {
  let client: ScrapeDoClient;

  // Helper function to create mock responses with proper headers
  const createMockResponse = (overrides: any = {}) => ({
    ok: true,
    status: 200,
    headers: new Headers({
      'Scrape.do-Request-Cost': '1',
      'Scrape.do-Remaining-Credits': '999'
    }),
    ...overrides
  });

  beforeEach(() => {
    client = new ScrapeDoClient();
    jest.clearAllMocks();
  });

  describe('Retry Logic', () => {
    it('should retry failed requests with exponential backoff', async () => {
      // First two attempts fail, third succeeds
      mockFetch
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockRejectedValueOnce(new Error('Connection refused'))
        .mockResolvedValueOnce(createMockResponse({
          text: jest.fn().mockResolvedValue('Success after retry')
        }));

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(true);
      expect(result.content).toBe('Success after retry');
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should fail after maximum retry attempts', async () => {
      mockFetch.mockRejectedValue(new Error('Persistent network error'));

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Persistent network error');
      expect(mockFetch).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });

    it('should implement exponential backoff delays', async () => {
      const startTime = Date.now();
      mockFetch.mockRejectedValue(new Error('Network error'));

      await client.scrapePage('https://example.com');

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should have waited for retries (at least 2000ms + 4000ms = 6000ms)
      expect(totalTime).toBeGreaterThan(6000);
    });

    it('should not retry on non-retryable errors', async () => {
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: jest.fn().mockResolvedValue('Invalid API key'),
        headers: new Headers()
      }));

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(mockFetch).toHaveBeenCalledTimes(1); // No retries for auth errors
    });
  });

  describe('Error Classification', () => {
    it('should classify network errors correctly', async () => {
      const networkErrors = [
        new Error('Network timeout'),
        new Error('Connection refused'),
        new Error('DNS resolution failed'),
        new Error('Socket hang up')
      ];

      for (const error of networkErrors) {
        mockFetch.mockRejectedValueOnce(error);
        
        const result = await client.scrapePage('https://example.com');
        
        expect(result.success).toBe(false);
        expect(result.error).toContain(error.message);
      }
    });

    it('should classify HTTP errors correctly', async () => {
      const httpErrors = [
        { status: 400, statusText: 'Bad Request', retryable: false },
        { status: 401, statusText: 'Unauthorized', retryable: false },
        { status: 403, statusText: 'Forbidden', retryable: false },
        { status: 404, statusText: 'Not Found', retryable: false },
        { status: 429, statusText: 'Too Many Requests', retryable: true },
        { status: 500, statusText: 'Internal Server Error', retryable: true },
        { status: 502, statusText: 'Bad Gateway', retryable: true },
        { status: 503, statusText: 'Service Unavailable', retryable: true }
      ];

      for (const httpError of httpErrors) {
        mockFetch.mockResolvedValueOnce(createMockResponse({
          ok: false,
          status: httpError.status,
          statusText: httpError.statusText,
          text: jest.fn().mockResolvedValue(`Error ${httpError.status}`),
          headers: new Headers()
        }));

        const result = await client.scrapePage('https://example.com');

        expect(result.success).toBe(false);
        expect(result.error).toContain(httpError.statusText);
      }
    });

    it('should handle timeout errors specifically', async () => {
      mockFetch.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');
    });
  });

  describe('Fallback Strategies', () => {
    it('should fallback to basic scraping when enhanced fails', async () => {
      // Mock enhanced scraping failure
      mockFetch
        .mockResolvedValueOnce(createMockResponse({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
          text: jest.fn().mockResolvedValue('Enhanced scraping failed'),
          headers: new Headers()
        }))
        .mockResolvedValueOnce(createMockResponse({
          text: jest.fn().mockResolvedValue('Basic scraping success')
        }));

      // Test with enhanced options first, then fallback
      const enhancedResult = await client.scrapePage('https://example.com', {
        enableJSRendering: true,
        waitCondition: 'networkidle2'
      });

      expect(enhancedResult.success).toBe(false);

      // Fallback to basic scraping
      const basicResult = await client.scrapePage('https://example.com', {
        enableJSRendering: false
      });

      expect(basicResult.success).toBe(true);
      expect(basicResult.content).toBe('Basic scraping success');
    });

    it('should fallback to datacenter proxy when residential fails', async () => {
      // Mock residential proxy failure
      mockFetch
        .mockResolvedValueOnce(createMockResponse({
          ok: false,
          status: 403,
          statusText: 'Forbidden',
          text: jest.fn().mockResolvedValue('Residential proxy blocked'),
          headers: new Headers()
        }))
        .mockResolvedValueOnce(createMockResponse({
          text: jest.fn().mockResolvedValue('Datacenter proxy success')
        }));

      // Try residential proxy first
      const residentialResult = await client.scrapePage('https://example.com', {
        useResidentialProxy: true
      });

      expect(residentialResult.success).toBe(false);

      // Fallback to datacenter proxy
      const datacenterResult = await client.scrapePage('https://example.com', {
        useResidentialProxy: false
      });

      expect(datacenterResult.success).toBe(true);
    });

    it('should handle API quota exhaustion gracefully', async () => {
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        text: jest.fn().mockResolvedValue('API quota exceeded'),
        headers: new Headers()
      }));

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Too Many Requests');
    });
  });

  describe('Enhanced Error Handling Integration', () => {
    it('should use CommonErrorHandlers for scraping operations', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Test error'));

      try {
        await CommonErrorHandlers.scraping.handleWithRetry(mockOperation);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }

      expect(mockOperation).toHaveBeenCalledTimes(4); // Initial + 3 retries
    });

    it('should create proper error context', () => {
      const context = ErrorHandlingUtils.createErrorContext({
        operation: 'web_scraping',
        provider: 'scrape.do',
        metadata: {
          url: 'https://example.com',
          options: { timeout: 30000 }
        }
      });

      expect(context.operation).toBe('web_scraping');
      expect(context.provider).toBe('scrape.do');
      expect(context.metadata?.url).toBe('https://example.com');
    });

    it('should format errors for logging', () => {
      const error = new Error('Test error');
      const context = { operation: 'test', provider: 'scrape.do' as const };

      const formatted = ErrorHandlingUtils.formatErrorForLogging(error, context);

      expect(formatted).toContain('Test error');
      expect(formatted).toContain('test');
      expect(formatted).toContain('scrape.do');
    });
  });

  describe('Recovery Mechanisms', () => {
    it('should recover from temporary network issues', async () => {
      let callCount = 0;
      mockFetch.mockImplementation(() => {
        callCount++;
        if (callCount <= 2) {
          return Promise.reject(new Error('Temporary network issue'));
        }
        return Promise.resolve(createMockResponse({
          text: jest.fn().mockResolvedValue('Recovered successfully')
        }));
      });

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(true);
      expect(result.content).toBe('Recovered successfully');
      expect(callCount).toBe(3);
    });

    it('should handle partial response data', async () => {
      mockFetch.mockResolvedValue(createMockResponse({
        text: jest.fn().mockResolvedValue('Partial content...')
      }));

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(true);
      expect(result.content).toBe('Partial content...');
    });

    it('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValue(createMockResponse({
        text: jest.fn().mockResolvedValue('Invalid JSON {')
      }));

      const result = await client.scrapePage('https://example.com', {
        returnJSON: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('JSON');
    });
  });

  describe('Error Reporting and Monitoring', () => {
    it('should log errors with appropriate detail', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      mockFetch.mockRejectedValue(new Error('Test error for logging'));

      await client.scrapePage('https://example.com');

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should include request context in error reports', async () => {
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: jest.fn().mockResolvedValue('Server error details'),
        headers: new Headers()
      }));

      const result = await client.scrapePage('https://example.com', {
        enableJSRendering: true,
        timeout: 30000
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('HTTP 500');
      expect(result.url).toBe('https://example.com');
    });

    it('should track error metrics', async () => {
      const errors = [
        new Error('Network error 1'),
        new Error('Network error 2'),
        new Error('Timeout error')
      ];

      for (const error of errors) {
        mockFetch.mockRejectedValueOnce(error);
        await client.scrapePage('https://example.com');
      }

      // Errors should be tracked for monitoring
      expect(mockFetch).toHaveBeenCalledTimes(9); // 3 errors × 3 attempts each
    });
  });

  describe('Graceful Degradation', () => {
    it('should degrade gracefully when advanced features fail', async () => {
      // Mock advanced feature failure but basic content success
      mockFetch.mockResolvedValue(createMockResponse({
        text: jest.fn().mockResolvedValue('Basic content without advanced features')
      }));

      const result = await client.scrapePage('https://example.com', {
        enableJSRendering: true,
        captureScreenshot: true,
        includeNetworkRequests: true
      });

      expect(result.success).toBe(true);
      expect(result.content).toBe('Basic content without advanced features');
    });

    it('should provide meaningful error messages', async () => {
      const testCases = [
        {
          mockResponse: { ok: false, status: 401, statusText: 'Unauthorized' },
          expectedMessage: 'Authentication failed'
        },
        {
          mockResponse: { ok: false, status: 429, statusText: 'Too Many Requests' },
          expectedMessage: 'Rate limit exceeded'
        },
        {
          mockResponse: { ok: false, status: 500, statusText: 'Internal Server Error' },
          expectedMessage: 'Server error'
        }
      ];

      for (const testCase of testCases) {
        mockFetch.mockResolvedValueOnce(createMockResponse({
          ...testCase.mockResponse,
          text: jest.fn().mockResolvedValue('Error details'),
          headers: new Headers()
        }));

        const result = await client.scrapePage('https://example.com');

        expect(result.success).toBe(false);
        expect(result.error).toContain(testCase.mockResponse.statusText);
      }
    });
  });

  describe('Circuit Breaker Pattern', () => {
    it('should implement circuit breaker for repeated failures', async () => {
      // Simulate repeated failures
      for (let i = 0; i < 5; i++) {
        mockFetch.mockRejectedValueOnce(new Error('Service unavailable'));
        await client.scrapePage('https://example.com');
      }

      // Circuit should be open, but we don't have circuit breaker implemented yet
      // This test documents the expected behavior
      expect(mockFetch).toHaveBeenCalledTimes(15); // 5 failures × 3 attempts each
    });
  });
});
