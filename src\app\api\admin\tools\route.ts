import { NextRequest, NextResponse } from 'next/server';
import { getAdminTools, updateTool, deleteTool, createTool } from '@/lib/supabase';
import { validateApiKey } from '@/lib/auth';
import { AdminToolFilters, ContentStatus } from '@/lib/types';
import { auditMiddleware } from '@/lib/audit/audit-middleware';

/**
 * GET /api/admin/tools
 * Get all tools for admin panel (including drafts and archived)
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse filters from query parameters
    const filters: AdminToolFilters = {
      category: searchParams.get('category') || undefined,
      subcategory: searchParams.get('subcategory') || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '100'),
      search: searchParams.get('search') || undefined,
      pricing: (searchParams.get('pricing') as 'free' | 'freemium' | 'paid' | 'open source' | 'subscription') || undefined,
      verified: searchParams.get('verified') === 'true' ? true : searchParams.get('verified') === 'false' ? false : undefined,
      sortBy: (searchParams.get('sortBy') as 'name' | 'rating' | 'newest' | 'popular' | 'created_at') || 'created_at',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      
      // Admin-specific filters
      aiGenerationStatus: (searchParams.get('aiGenerationStatus') as 'pending' | 'processing' | 'completed' | 'failed' | 'skipped') || undefined,
      contentStatus: (searchParams.get('contentStatus') as ContentStatus) || undefined,
      submissionType: (searchParams.get('submissionType') as 'admin' | 'user_url' | 'user_full') || undefined,
      hasEditorialReview: searchParams.get('hasEditorialReview') === 'true' ? true : searchParams.get('hasEditorialReview') === 'false' ? false : undefined,
      qualityScoreMin: searchParams.get('qualityScoreMin') ? parseInt(searchParams.get('qualityScoreMin')!) : undefined,
      qualityScoreMax: searchParams.get('qualityScoreMax') ? parseInt(searchParams.get('qualityScoreMax')!) : undefined,
      lastScrapedAfter: searchParams.get('lastScrapedAfter') || undefined,
      lastScrapedBefore: searchParams.get('lastScrapedBefore') || undefined,
    };

    const result = await getAdminTools(filters);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching admin tools:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tools' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/tools
 * Create a new tool (admin only)
 */
export const POST = auditMiddleware.withAudit(async (request: NextRequest) => {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const toolData = await request.json();

    // Generate slug from name if not provided
    if (!toolData.slug && toolData.name) {
      toolData.slug = toolData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-|-$/g, '');
    }

    // Set default values for admin-created tools
    const newToolData = {
      ...toolData,
      content_status: toolData.content_status || 'draft',
      submission_type: toolData.submission_type || 'admin',
      submission_source: toolData.submission_source || 'admin_panel',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const newTool = await createTool(newToolData);

    return NextResponse.json({
      success: true,
      data: newTool,
      message: 'Tool created successfully',
    });
  } catch (error) {
    console.error('Error creating tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create tool' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/admin/tools/[id]
 * Update a tool (admin only)
 */
export const PUT = auditMiddleware.withAudit(async (request: NextRequest) => {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const toolId = pathSegments[pathSegments.length - 1];

    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    const updates = await request.json();
    const updatedTool = await updateTool(toolId, updates);

    return NextResponse.json({
      success: true,
      data: updatedTool,
    });
  } catch (error) {
    console.error('Error updating tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update tool' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/admin/tools/[id]
 * Delete a tool (admin only)
 */
export const DELETE = auditMiddleware.withAudit(async (request: NextRequest) => {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const toolId = pathSegments[pathSegments.length - 1];

    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    await deleteTool(toolId);

    return NextResponse.json({
      success: true,
      message: 'Tool deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete tool' },
      { status: 500 }
    );
  }
});
