/**
 * Unit Tests for Scrape.do API Client
 * Tests API integration, authentication, response handling, and enhanced scraping parameters
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ScrapeDoClient } from '@/lib/scraping/scrape-do-client';
import { ScrapeOptions, ScrapeResult, UsageStats } from '@/lib/scraping/types';

// Mock fetch globally
const mockFetch = jest.fn() as jest.MockedFunction<typeof fetch>;
global.fetch = mockFetch;

// Mock environment variables
const mockEnv = {
  SCRAPE_DO_API_KEY: 'test-api-key-12345',
  SCRAPE_DO_BASE_URL: 'https://api.scrape.do'
};

Object.defineProperty(process, 'env', {
  value: { ...process.env, ...mockEnv }
});

describe('ScrapeDoClient', () => {
  let client: ScrapeDoClient;

  // Helper function to create mock responses with proper headers
  const createMockResponse = (overrides: any = {}) => ({
    ok: true,
    status: 200,
    headers: new Headers({
      'Scrape.do-Request-Cost': '1',
      'Scrape.do-Remaining-Credits': '999'
    }),
    ...overrides
  });

  beforeEach(() => {
    client = new ScrapeDoClient();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Constructor and Configuration', () => {
    it('should initialize with correct default configuration', () => {
      expect(client).toBeInstanceOf(ScrapeDoClient);
    });

    it('should use environment variables for configuration', () => {
      // Test that the client uses the mocked environment variables
      expect(process.env.SCRAPE_DO_API_KEY).toBe('test-api-key-12345');
      expect(process.env.SCRAPE_DO_BASE_URL).toBe('https://api.scrape.do');
    });
  });

  describe('Basic Scraping Functionality', () => {
    it('should perform basic scraping with minimal options', async () => {
      const mockResponse = createMockResponse({
        text: jest.fn().mockResolvedValue('# Test Content\n\nThis is test content.')
      });
      mockFetch.mockResolvedValue(mockResponse as any);

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(true);
      expect(result.content).toBe('# Test Content\n\nThis is test content.');
      expect(result.url).toBe('https://example.com');
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    it('should handle API errors gracefully', async () => {
      const mockResponse = createMockResponse({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: jest.fn().mockResolvedValue('Invalid URL provided'),
        headers: new Headers()
      });
      mockFetch.mockResolvedValue(mockResponse as any);

      const result = await client.scrapePage('https://invalid-url');

      expect(result.success).toBe(false);
      expect(result.error).toContain('HTTP 400: Bad Request');
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error');
    });
  });

  describe('Enhanced Scraping with render=true', () => {
    it('should configure enhanced scraping options correctly', async () => {
      const mockResponse = createMockResponse({
        text: jest.fn().mockResolvedValue('Enhanced content with JavaScript'),
        headers: new Headers({
          'Scrape.do-Request-Cost': '5',
          'Scrape.do-Remaining-Credits': '995'
        })
      });
      mockFetch.mockResolvedValue(mockResponse as any);

      const options: ScrapeOptions = {
        enableJSRendering: true,
        waitCondition: 'networkidle0',
        customWaitTime: 3000,
        blockResources: true,
        timeout: 45000
      };

      const result = await client.scrapePage('https://spa-example.com', options);

      expect(result.success).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(1);
      
      // Verify the URL contains the correct parameters
      const callArgs = mockFetch.mock.calls[0];
      const requestUrl = callArgs[0] as string;
      
      expect(requestUrl).toContain('render=true');
      expect(requestUrl).toContain('waitUntil=networkidle0');
      expect(requestUrl).toContain('customWait=3000');
      expect(requestUrl).toContain('blockResources=true');
      expect(requestUrl).toContain('timeout=45000');
    });

    it('should handle different wait conditions', async () => {
      const mockResponse = createMockResponse({
        text: jest.fn().mockResolvedValue('Content loaded')
      });
      mockFetch.mockResolvedValue(mockResponse as any);

      const waitConditions = ['domcontentloaded', 'load', 'networkidle0', 'networkidle2'] as const;

      for (const waitCondition of waitConditions) {
        mockFetch.mockClear();
        
        const options: ScrapeOptions = {
          enableJSRendering: true,
          waitCondition
        };

        await client.scrapePage('https://example.com', options);

        const callArgs = mockFetch.mock.calls[0];
        const requestUrl = callArgs[0] as string;
        expect(requestUrl).toContain(`waitUntil=${waitCondition}`);
      }
    });

    it('should configure device types correctly', async () => {
      const mockResponse = createMockResponse({
        text: jest.fn().mockResolvedValue('Device-specific content')
      });
      mockFetch.mockResolvedValue(mockResponse as any);

      const deviceTypes = ['desktop', 'mobile', 'tablet'] as const;

      for (const deviceType of deviceTypes) {
        mockFetch.mockClear();
        
        const options: ScrapeOptions = {
          deviceType
        };

        await client.scrapePage('https://example.com', options);

        const callArgs = mockFetch.mock.calls[0];
        const requestUrl = callArgs[0] as string;
        expect(requestUrl).toContain(`device=${deviceType}`);
      }
    });
  });

  describe('Proxy Configuration', () => {
    it('should configure residential proxy correctly', async () => {
      const mockResponse = createMockResponse({
        text: jest.fn().mockResolvedValue('Proxy content')
      });
      mockFetch.mockResolvedValue(mockResponse as any);

      const options: ScrapeOptions = {
        useResidentialProxy: true,
        geoTargeting: 'us',
        stickySession: 12345
      };

      await client.scrapePage('https://example.com', options);

      const callArgs = mockFetch.mock.calls[0];
      const requestUrl = callArgs[0] as string;
      
      expect(requestUrl).toContain('super=true');
      expect(requestUrl).toContain('geoCode=us');
      expect(requestUrl).toContain('sessionId=12345');
    });

    it('should default to datacenter proxy when residential is false', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        text: jest.fn().mockResolvedValue('Datacenter content')
      };
      mockFetch.mockResolvedValue(mockResponse as any);

      const options: ScrapeOptions = {
        useResidentialProxy: false
      };

      await client.scrapePage('https://example.com', options);

      const callArgs = mockFetch.mock.calls[0];
      const requestUrl = callArgs[0] as string;
      
      expect(requestUrl).not.toContain('super=true');
    });
  });

  describe('Screenshot Functionality', () => {
    it('should handle JSON response for screenshots', async () => {
      const mockJsonResponse = {
        content: 'Page content',
        screenShots: [
          {
            type: 'ScreenShot',
            image: 'base64-encoded-image-data'
          }
        ]
      };

      const mockResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue(mockJsonResponse),
        text: jest.fn().mockResolvedValue(JSON.stringify(mockJsonResponse))
      };
      mockFetch.mockResolvedValue(mockResponse as any);

      const options: ScrapeOptions = {
        captureScreenshot: true,
        returnJSON: true
      };

      const result = await client.scrapePage('https://example.com', options);

      expect(result.success).toBe(true);
      expect(result.content).toBe('Page content');
      expect(result.screenShots).toHaveLength(1);
      expect(result.screenShots![0].type).toBe('ScreenShot');
    });

    it('should handle full page screenshots', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        text: jest.fn().mockResolvedValue('Content with full screenshot')
      };
      mockFetch.mockResolvedValue(mockResponse as any);

      const options: ScrapeOptions = {
        fullPageScreenshot: true,
        returnJSON: true
      };

      await client.scrapePage('https://example.com', options);

      const callArgs = mockFetch.mock.calls[0];
      const requestUrl = callArgs[0] as string;
      
      expect(requestUrl).toContain('fullScreenShot=true');
      expect(requestUrl).toContain('returnJSON=true');
    });
  });

  describe('Usage Statistics', () => {
    it('should fetch usage statistics', async () => {
      const mockStatsResponse = {
        IsActive: true,
        ConcurrentRequest: 10,
        MaxMonthlyRequest: 1500,
        RemainingConcurrentRequest: 8,
        RemainingMonthlyRequest: 1000
      };

      const mockResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue(mockStatsResponse)
      };
      mockFetch.mockResolvedValue(mockResponse as any);

      const stats = await client.getUsageStatistics();

      expect(stats.isActive).toBe(true);
      expect(stats.concurrentRequests).toBe(10);
      expect(stats.maxMonthlyRequests).toBe(1500);
      expect(stats.remainingConcurrentRequests).toBe(8);
      expect(stats.remainingMonthlyRequests).toBe(1000);
    });

    it('should handle usage statistics errors', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      };
      mockFetch.mockResolvedValue(mockResponse as any);

      await expect(client.getUsageStatistics()).rejects.toThrow('Failed to fetch usage stats');
    });
  });

  describe('Retry Logic', () => {
    it('should retry failed requests', async () => {
      // First two calls fail, third succeeds
      mockFetch
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockRejectedValueOnce(new Error('Connection refused'))
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          text: jest.fn().mockResolvedValue('Success after retry')
        } as any);

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(true);
      expect(result.content).toBe('Success after retry');
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should fail after maximum retry attempts', async () => {
      mockFetch.mockRejectedValue(new Error('Persistent network error'));

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Persistent network error');
      expect(mockFetch).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
  });

  describe('Parameter Building', () => {
    it('should build request parameters correctly', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        text: jest.fn().mockResolvedValue('Test content')
      };
      mockFetch.mockResolvedValue(mockResponse as any);

      const options: ScrapeOptions = {
        enableJSRendering: true,
        waitCondition: 'networkidle2',
        customWaitTime: 5000,
        blockResources: true,
        timeout: 60000,
        deviceType: 'mobile',
        outputFormat: 'markdown',
        useResidentialProxy: true,
        geoTargeting: 'uk'
      };

      await client.scrapePage('https://example.com', options);

      const callArgs = mockFetch.mock.calls[0];
      const requestUrl = callArgs[0] as string;
      
      expect(requestUrl).toContain('url=https%3A%2F%2Fexample.com');
      expect(requestUrl).toContain('render=true');
      expect(requestUrl).toContain('waitUntil=networkidle2');
      expect(requestUrl).toContain('customWait=5000');
      expect(requestUrl).toContain('blockResources=true');
      expect(requestUrl).toContain('timeout=60000');
      expect(requestUrl).toContain('device=mobile');
      expect(requestUrl).toContain('output=markdown');
      expect(requestUrl).toContain('super=true');
      expect(requestUrl).toContain('geoCode=uk');
    });
  });
});
