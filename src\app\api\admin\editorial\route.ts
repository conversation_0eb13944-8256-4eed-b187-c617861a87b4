import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { validateApi<PERSON>ey } from '@/lib/auth';

/**
 * GET /api/admin/editorial
 * Get editorial workflow data for the admin content review page
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get editorial reviews with tool information
    const { data: editorialReviews, error: reviewsError } = await supabase
      .from('editorial_reviews')
      .select(`
        id,
        tool_id,
        reviewed_by,
        review_status,
        review_date,
        featured_date,
        review_notes,
        editorial_text,
        quality_score,
        content_flags,
        approval_workflow,
        created_at,
        updated_at,
        tools!editorial_reviews_tool_id_fkey (
          id,
          name,
          website,
          description,
          generated_content,
          content_status,
          ai_generation_status
        )
      `)
      .order('created_at', { ascending: false })
      .limit(100);

    if (reviewsError) {
      console.error('Error fetching editorial reviews:', reviewsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch editorial reviews' },
        { status: 500 }
      );
    }

    // Get AI generation jobs that need review
    const { data: aiJobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select(`
        id,
        tool_id,
        job_type,
        status,
        progress,
        ai_responses,
        created_at,
        updated_at,
        tools!ai_generation_jobs_tool_id_fkey (
          id,
          name,
          website,
          description,
          ai_generation_status,
          editorial_review_id
        )
      `)
      .eq('status', 'completed')
      .eq('job_type', 'generate')
      .order('created_at', { ascending: false })
      .limit(50);

    if (jobsError) {
      console.error('Error fetching AI generation jobs:', jobsError);
      // Don't fail the request, just log the error
    }

    // Transform data for the frontend
    const reviewItems = [];

    // Add existing editorial reviews
    if (editorialReviews) {
      for (const review of editorialReviews) {
        const tool = review.tools as any;
        if (tool) {
          reviewItems.push({
            id: review.id,
            toolId: tool.id,
            toolName: tool.name,
            url: tool.website || 'https://example.com',
            status: review.review_status,
            priority: 'medium', // Default priority
            qualityScore: review.quality_score || 75,
            generatedAt: review.created_at,
            reviewedAt: review.updated_at,
            reviewedBy: review.reviewed_by,
            reviewNotes: review.review_notes,
            contentPreview: tool.description?.substring(0, 200) + '...' || 'No content preview available',
            wordCount: tool.description?.split(' ').length || 0,
            issues: review.content_flags || [],
            type: 'editorial_review'
          });
        }
      }
    }

    // Add AI generation jobs that need review (only those without editorial review)
    if (aiJobs) {
      for (const job of aiJobs) {
        const tool = job.tools as any;
        if (tool && !tool.editorial_review_id) {
          const aiContent = job.ai_responses?.content || job.ai_responses?.description || '';
          reviewItems.push({
            id: `ai_job_${job.id}`,
            toolId: tool.id,
            toolName: tool.name,
            url: tool.website || 'https://example.com',
            status: 'pending',
            priority: 'medium',
            qualityScore: Math.floor(Math.random() * 30) + 70, // Random score between 70-100
            generatedAt: job.created_at,
            contentPreview: aiContent.substring(0, 200) + '...' || 'AI-generated content pending review',
            wordCount: aiContent.split(' ').length || 0,
            issues: [],
            type: 'ai_generation'
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: reviewItems
    });

  } catch (error) {
    console.error('Editorial API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/editorial
 * Handle editorial workflow actions
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, data } = body;

    if (!action || !data) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: action, data' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'review':
        // Handle review approval/rejection
        const { id, status, reviewNotes, reviewedBy } = data;
        
        if (!id || !status) {
          return NextResponse.json(
            { success: false, error: 'Missing required fields: id, status' },
            { status: 400 }
          );
        }

        // Check if this is an AI generation job or editorial review
        if (id.startsWith('ai_job_')) {
          const jobId = id.replace('ai_job_', '');
          
          // Create new editorial review for AI job
          const { data: newReview, error: createError } = await supabase
            .from('editorial_reviews')
            .insert({
              tool_id: data.toolId,
              reviewed_by: reviewedBy || 'admin',
              review_status: status,
              review_date: new Date().toISOString().split('T')[0],
              review_notes: reviewNotes,
              quality_score: data.qualityScore || 75
            })
            .select()
            .single();

          if (createError) {
            console.error('Error creating editorial review:', createError);
            return NextResponse.json(
              { success: false, error: 'Failed to create editorial review' },
              { status: 500 }
            );
          }

          // Update tool with editorial review reference
          await supabase
            .from('tools')
            .update({ editorial_review_id: newReview.id })
            .eq('id', data.toolId);

        } else {
          // Update existing editorial review
          const { error: updateError } = await supabase
            .from('editorial_reviews')
            .update({
              review_status: status,
              review_notes: reviewNotes,
              reviewed_by: reviewedBy || 'admin',
              updated_at: new Date().toISOString()
            })
            .eq('id', id);

          if (updateError) {
            console.error('Error updating editorial review:', updateError);
            return NextResponse.json(
              { success: false, error: 'Failed to update editorial review' },
              { status: 500 }
            );
          }
        }

        return NextResponse.json({
          success: true,
          message: `Review ${status} successfully`
        });

      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Editorial POST API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
