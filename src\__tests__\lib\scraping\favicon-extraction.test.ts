/**
 * Unit Tests for Favicon Extraction
 * Tests favicon discovery, extraction priority system, fallback mechanisms, and URL validation
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { MediaExtractor } from '@/lib/scraping/media-extractor';
import { FaviconResult, ImageCollection } from '@/lib/scraping/types';

// Mock the scrape-do client
const mockScrapeDoClient = {
  scrapePage: jest.fn()
};

jest.mock('@/lib/scraping/scrape-do-client', () => ({
  scrapeDoClient: mockScrapeDoClient
}));

// Mock the favicon collector
const mockFaviconCollector = {
  collectFavicon: jest.fn()
};

jest.mock('@/lib/scraping/favicon-collector', () => ({
  faviconCollector: mockFaviconCollector
}));

// Mock the OG image handler
const mockOgImageHandler = {
  extractOGImages: jest.fn()
};

jest.mock('@/lib/scraping/og-image-handler', () => ({
  ogImageHandler: mockOgImageHandler
}));

// Mock fetch for URL validation
const mockFetch = jest.fn();
global.fetch = mockFetch as any;

describe('Favicon Extraction', () => {
  let mediaExtractor: MediaExtractor;

  beforeEach(() => {
    mediaExtractor = new MediaExtractor();
    jest.clearAllMocks();
  });

  describe('Favicon Discovery', () => {
    it('should extract favicon from link tags', async () => {
      const htmlContent = `
        <html>
          <head>
            <link rel="icon" type="image/x-icon" href="/favicon.ico">
            <link rel="shortcut icon" href="/favicon-16x16.png">
            <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
          </head>
        </html>
      `;

      const expectedFaviconUrls = [
        'https://example.com/favicon.ico',
        'https://example.com/favicon-16x16.png',
        'https://example.com/apple-touch-icon.png'
      ];

      mockFaviconCollector.collectFavicon.mockResolvedValue({
        faviconUrls: expectedFaviconUrls,
        primaryFavicon: expectedFaviconUrls[0],
        extractedAt: new Date().toISOString()
      });

      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

      expect(result.favicon).toEqual(expectedFaviconUrls);
      expect(mockFaviconCollector.collectFavicon).toHaveBeenCalledWith('https://example.com', htmlContent);
    });

    it('should handle different favicon formats', async () => {
      const testCases = [
        {
          html: '<link rel="icon" type="image/png" href="/favicon.png">',
          expected: ['https://example.com/favicon.png']
        },
        {
          html: '<link rel="icon" type="image/svg+xml" href="/favicon.svg">',
          expected: ['https://example.com/favicon.svg']
        },
        {
          html: '<link rel="icon" type="image/gif" href="/favicon.gif">',
          expected: ['https://example.com/favicon.gif']
        },
        {
          html: '<link rel="shortcut icon" href="/favicon.ico">',
          expected: ['https://example.com/favicon.ico']
        }
      ];

      for (const testCase of testCases) {
        mockFaviconCollector.collectFavicon.mockResolvedValue({
          faviconUrls: testCase.expected,
          primaryFavicon: testCase.expected[0],
          extractedAt: new Date().toISOString()
        });

        mockOgImageHandler.extractOGImages.mockReturnValue([]);

        const result = await mediaExtractor.collectImagesWithPriority('https://example.com', testCase.html);

        expect(result.favicon).toEqual(testCase.expected);
      }
    });

    it('should handle multiple favicon sizes', async () => {
      const htmlContent = `
        <html>
          <head>
            <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
            <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
            <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
            <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
          </head>
        </html>
      `;

      const expectedFaviconUrls = [
        'https://example.com/favicon-32x32.png',
        'https://example.com/favicon-16x16.png',
        'https://example.com/apple-touch-icon.png',
        'https://example.com/apple-touch-icon-152x152.png'
      ];

      mockFaviconCollector.collectFavicon.mockResolvedValue({
        faviconUrls: expectedFaviconUrls,
        primaryFavicon: expectedFaviconUrls[0],
        extractedAt: new Date().toISOString()
      });

      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

      expect(result.favicon).toEqual(expectedFaviconUrls);
    });
  });

  describe('Favicon Priority System', () => {
    it('should prioritize favicon over screenshot', async () => {
      const htmlContent = '<link rel="icon" href="/favicon.ico">';

      mockFaviconCollector.collectFavicon.mockResolvedValue({
        faviconUrls: ['https://example.com/favicon.ico'],
        primaryFavicon: 'https://example.com/favicon.ico',
        extractedAt: new Date().toISOString()
      });

      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

      expect(result.favicon).toEqual(['https://example.com/favicon.ico']);
      expect(result.screenshot).toBeNull(); // Should not capture screenshot when favicon is available
    });

    it('should fallback to screenshot when no favicon found', async () => {
      const htmlContent = '<html><head></head><body>No favicon</body></html>';

      mockFaviconCollector.collectFavicon.mockResolvedValue({
        faviconUrls: [],
        primaryFavicon: null,
        extractedAt: new Date().toISOString()
      });

      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      // Mock screenshot capture
      mockScrapeDoClient.scrapePage.mockResolvedValue({
        success: true,
        content: 'Page content',
        screenShots: [{
          type: 'ScreenShot',
          image: 'base64-screenshot-data'
        }]
      });

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

      expect(result.favicon).toBeNull();
      expect(result.screenshot).toBeDefined();
      expect(result.screenshot?.success).toBe(true);
    });
  });

  describe('Fallback Mechanisms', () => {
    it('should fallback to default favicon.ico when no favicon links found', async () => {
      const htmlContent = '<html><head></head><body>No favicon links</body></html>';

      mockFaviconCollector.collectFavicon.mockResolvedValue({
        faviconUrls: ['https://example.com/favicon.ico'], // Default fallback
        primaryFavicon: 'https://example.com/favicon.ico',
        extractedAt: new Date().toISOString()
      });

      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

      expect(result.favicon).toEqual(['https://example.com/favicon.ico']);
    });

    it('should handle favicon extraction errors gracefully', async () => {
      const htmlContent = '<link rel="icon" href="/favicon.ico">';

      mockFaviconCollector.collectFavicon.mockRejectedValue(new Error('Favicon extraction failed'));
      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

      expect(result.favicon).toBeNull();
      expect(result.ogImages).toEqual([]);
    });

    it('should handle network errors during favicon validation', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      const isValid = await mediaExtractor.validateImageUrl('https://example.com/favicon.ico');

      expect(isValid).toBe(false);
    });
  });

  describe('URL Validation', () => {
    it('should validate accessible favicon URLs', async () => {
      const mockResponse = {
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue('image/x-icon')
        }
      };
      mockFetch.mockResolvedValue(mockResponse);

      const isValid = await mediaExtractor.validateImageUrl('https://example.com/favicon.ico');

      expect(isValid).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith('https://example.com/favicon.ico', {
        method: 'HEAD',
        signal: expect.any(AbortSignal)
      });
    });

    it('should reject non-image content types', async () => {
      const mockResponse = {
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue('text/html')
        }
      };
      mockFetch.mockResolvedValue(mockResponse);

      const isValid = await mediaExtractor.validateImageUrl('https://example.com/not-an-image.html');

      expect(isValid).toBe(false);
    });

    it('should handle 404 responses', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        headers: {
          get: jest.fn().mockReturnValue('image/x-icon')
        }
      };
      mockFetch.mockResolvedValue(mockResponse);

      const isValid = await mediaExtractor.validateImageUrl('https://example.com/missing-favicon.ico');

      expect(isValid).toBe(false);
    });

    it('should handle timeout during validation', async () => {
      mockFetch.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );

      const isValid = await mediaExtractor.validateImageUrl('https://slow-example.com/favicon.ico');

      expect(isValid).toBe(false);
    });
  });

  describe('Relative URL Resolution', () => {
    it('should resolve relative favicon URLs correctly', async () => {
      const testCases = [
        {
          baseUrl: 'https://example.com',
          faviconHref: '/favicon.ico',
          expected: 'https://example.com/favicon.ico'
        },
        {
          baseUrl: 'https://example.com/path/',
          faviconHref: '../favicon.ico',
          expected: 'https://example.com/favicon.ico'
        },
        {
          baseUrl: 'https://example.com/path/page.html',
          faviconHref: './favicon.ico',
          expected: 'https://example.com/path/favicon.ico'
        },
        {
          baseUrl: 'https://example.com',
          faviconHref: 'favicon.ico',
          expected: 'https://example.com/favicon.ico'
        }
      ];

      for (const testCase of testCases) {
        const htmlContent = `<link rel="icon" href="${testCase.faviconHref}">`;

        mockFaviconCollector.collectFavicon.mockResolvedValue({
          faviconUrls: [testCase.expected],
          primaryFavicon: testCase.expected,
          extractedAt: new Date().toISOString()
        });

        mockOgImageHandler.extractOGImages.mockReturnValue([]);

        const result = await mediaExtractor.collectImagesWithPriority(testCase.baseUrl, htmlContent);

        expect(result.favicon).toEqual([testCase.expected]);
      }
    });
  });

  describe('Favicon Formats and Types', () => {
    it('should handle various favicon MIME types', async () => {
      const faviconTypes = [
        { type: 'image/x-icon', href: '/favicon.ico' },
        { type: 'image/png', href: '/favicon.png' },
        { type: 'image/svg+xml', href: '/favicon.svg' },
        { type: 'image/gif', href: '/favicon.gif' },
        { type: 'image/jpeg', href: '/favicon.jpg' }
      ];

      for (const favicon of faviconTypes) {
        const htmlContent = `<link rel="icon" type="${favicon.type}" href="${favicon.href}">`;
        const expectedUrl = `https://example.com${favicon.href}`;

        mockFaviconCollector.collectFavicon.mockResolvedValue({
          faviconUrls: [expectedUrl],
          primaryFavicon: expectedUrl,
          extractedAt: new Date().toISOString()
        });

        mockOgImageHandler.extractOGImages.mockReturnValue([]);

        const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

        expect(result.favicon).toEqual([expectedUrl]);
      }
    });

    it('should handle Apple touch icons', async () => {
      const htmlContent = `
        <html>
          <head>
            <link rel="apple-touch-icon" href="/apple-touch-icon.png">
            <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
            <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon-180x180.png">
          </head>
        </html>
      `;

      const expectedUrls = [
        'https://example.com/apple-touch-icon.png',
        'https://example.com/apple-touch-icon-152x152.png',
        'https://example.com/apple-touch-icon-180x180.png'
      ];

      mockFaviconCollector.collectFavicon.mockResolvedValue({
        faviconUrls: expectedUrls,
        primaryFavicon: expectedUrls[0],
        extractedAt: new Date().toISOString()
      });

      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

      expect(result.favicon).toEqual(expectedUrls);
    });
  });

  describe('Error Handling', () => {
    it('should continue processing when favicon extraction fails', async () => {
      const htmlContent = '<link rel="icon" href="/favicon.ico">';

      mockFaviconCollector.collectFavicon.mockRejectedValue(new Error('Extraction failed'));
      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', htmlContent);

      expect(result).toBeDefined();
      expect(result.favicon).toBeNull();
      expect(result.ogImages).toEqual([]);
    });

    it('should handle malformed HTML gracefully', async () => {
      const malformedHtml = '<html><head><link rel="icon" href="/favicon.ico"<body>Malformed</body></html>';

      mockFaviconCollector.collectFavicon.mockResolvedValue({
        faviconUrls: [],
        primaryFavicon: null,
        extractedAt: new Date().toISOString()
      });

      mockOgImageHandler.extractOGImages.mockReturnValue([]);

      const result = await mediaExtractor.collectImagesWithPriority('https://example.com', malformedHtml);

      expect(result).toBeDefined();
      expect(result.favicon).toBeNull();
    });
  });
});
