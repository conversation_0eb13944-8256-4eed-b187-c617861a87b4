#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function applyMigration002Manually() {
  console.log('🚀 Manually applying Migration 002: Add Environment and Configuration Columns');
  console.log('============================================================');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Step 1: Add environment column
    console.log('📋 Step 1: Adding environment column...');
    
    const { error: envColumnError } = await supabase.rpc('exec_sql', {
      sql: `ALTER TABLE system_configuration ADD COLUMN IF NOT EXISTS environment VARCHAR(50) DEFAULT 'development';`
    });

    if (envColumnError) {
      console.log('⚠️  RPC not available, trying direct approach...');
      
      // Try using a simple insert/update to test if column exists
      const { error: testError } = await supabase
        .from('system_configuration')
        .update({ environment: 'development' })
        .eq('config_key', 'test_nonexistent_key');

      if (testError && testError.message.includes('column "environment" does not exist')) {
        console.log('❌ Environment column does not exist and cannot be added via Supabase client');
        console.log('🔧 You need to run this SQL manually in Supabase SQL Editor:');
        console.log('   ALTER TABLE system_configuration ADD COLUMN IF NOT EXISTS environment VARCHAR(50) DEFAULT \'development\';');
      } else {
        console.log('✅ Environment column already exists or was added');
      }
    } else {
      console.log('✅ Environment column added successfully');
    }

    // Step 2: Add configuration column
    console.log('📋 Step 2: Adding configuration column...');
    
    const { error: configColumnError } = await supabase.rpc('exec_sql', {
      sql: `ALTER TABLE system_configuration ADD COLUMN IF NOT EXISTS configuration JSONB;`
    });

    if (configColumnError) {
      console.log('⚠️  RPC not available, trying direct approach...');
      
      // Try using a simple insert/update to test if column exists
      const { error: testError } = await supabase
        .from('system_configuration')
        .update({ configuration: {} })
        .eq('config_key', 'test_nonexistent_key');

      if (testError && testError.message.includes('column "configuration" does not exist')) {
        console.log('❌ Configuration column does not exist and cannot be added via Supabase client');
        console.log('🔧 You need to run this SQL manually in Supabase SQL Editor:');
        console.log('   ALTER TABLE system_configuration ADD COLUMN IF NOT EXISTS configuration JSONB;');
        console.log('');
        console.log('📋 Complete SQL to run in Supabase SQL Editor:');
        console.log('============================================================');
        console.log(`
-- Migration 002: Add Environment and Configuration Columns
-- Add environment column to system_configuration table
ALTER TABLE system_configuration 
ADD COLUMN IF NOT EXISTS environment VARCHAR(50) DEFAULT 'development';

-- Add configuration column to store complete configuration objects
ALTER TABLE system_configuration 
ADD COLUMN IF NOT EXISTS configuration JSONB;

-- Create index for environment column for better query performance
CREATE INDEX IF NOT EXISTS idx_system_configuration_environment 
ON system_configuration(environment);

-- Create unique constraint for environment (one config per environment)
CREATE UNIQUE INDEX IF NOT EXISTS idx_system_configuration_environment_unique 
ON system_configuration(environment) 
WHERE configuration IS NOT NULL;
        `);
        console.log('============================================================');
        console.log('');
        console.log('⚠️  After running the SQL above, run this script again to insert the default configuration.');
        return;
      } else {
        console.log('✅ Configuration column already exists or was added');
      }
    } else {
      console.log('✅ Configuration column added successfully');
    }

    // Step 3: Insert default configuration
    console.log('📋 Step 3: Inserting default configuration...');
    
    const defaultConfig = {
      config_key: 'default_environment_config',
      config_value: {},
      config_type: 'system',
      environment: 'development',
      configuration: {
        aiGeneration: {
          providers: {
            openai: {
              enabled: true,
              model: 'gpt-4o-2024-11-20',
              maxTokens: 16384,
              temperature: 0.7,
              timeout: 60000,
              priority: 1
            },
            openrouter: {
              enabled: true,
              model: 'google/gemini-2.5-pro-preview',
              maxTokens: 65536,
              temperature: 0.7,
              implicitCaching: true,
              timeout: 120000,
              priority: 2
            }
          },
          modelSelection: {
            strategy: 'auto',
            fallbackOrder: ['openai', 'openrouter'],
            costThreshold: 0.01,
            qualityThreshold: 0.8
          },
          contentGeneration: {
            autoApproval: false,
            qualityThreshold: 0.8,
            editorialReviewRequired: true,
            maxRetries: 3,
            timeoutSeconds: 300
          }
        }
      },
      description: 'Default configuration for environment-based configuration management'
    };

    const { error: insertError } = await supabase
      .from('system_configuration')
      .upsert(defaultConfig, { onConflict: 'config_key' });

    if (insertError) {
      console.log('❌ Failed to insert default configuration:', insertError.message);
      if (insertError.message.includes('column') && insertError.message.includes('does not exist')) {
        console.log('🔧 The columns were not created successfully. Please run the SQL manually first.');
      }
    } else {
      console.log('✅ Default configuration inserted successfully');
    }

    console.log('🎉 Migration 002 completed!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

applyMigration002Manually().catch(console.error);
